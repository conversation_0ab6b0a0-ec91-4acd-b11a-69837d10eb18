# AI合同审核系统

基于千问3大语言模型的智能合同分析系统，提供合同要素提取、风险识别和缺失条款检测功能。

## 🚀 快速开始

### 1. 进入MVP目录
```bash
cd mvp
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置千问3 API
```bash
python setup_qwen.py
```

### 4. 启动服务
```bash
python start_mvp.py
```

### 5. 访问系统
- **Web界面**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/api/v1/health

## 📁 项目结构

- **mvp/**: MVP实现代码和文档
- **dosc/**: 项目需求、设计和计划文档
- **技术验证报告.md**: 技术验证总结
- **PROJECT_STRUCTURE.md**: 详细的项目结构说明

## 🧪 运行测试

```bash
cd mvp
python -m pytest test_mvp.py -v
```

## 📋 核心功能

- ✅ Word文档解析和结构化处理
- ✅ 千问3 AI智能分析
- ✅ 合同要素自动提取
- ✅ 合同类型识别
- ✅ 异步处理和API调用
- ✅ 错误处理和降级机制

## 📖 详细文档

- [MVP使用说明](mvp/README.md)
- [项目结构说明](PROJECT_STRUCTURE.md)
- [需求文档](dosc/需求文档.md)
- [设计文档](dosc/设计文档.md)

## 🔧 技术栈

- **后端**: Python 3.8+, FastAPI
- **AI服务**: 千问3 (Qwen) API
- **文档处理**: python-docx
- **测试**: pytest
- **前端**: 简单HTML界面

## ⚠️ 注意事项

1. 需要有效的千问3 API密钥
2. 仅支持Word文档格式(.docx)
3. 文件大小限制50MB
4. 需要网络连接进行AI分析

## 📄 许可证

本项目仅用于技术验证和学习目的。
