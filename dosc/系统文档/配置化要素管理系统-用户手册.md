# 配置化要素管理系统 - 用户操作手册

## 1. 系统简介

### 1.1 系统概述
配置化要素管理系统是AI合同审核系统的核心配置模块，允许用户通过可视化界面配置合同要素的提取规则和验证规则，无需修改代码即可支持新的合同类型和要素。

### 1.2 主要功能
- **合同类型管理**：配置不同类型的合同及其识别规则
- **要素分类管理**：管理要素的层级分类结构
- **要素模板配置**：定义要素的基本信息和提取配置
- **提取规则配置**：配置要素的提取规则（正则表达式、关键词等）
- **验证规则配置**：配置要素的验证规则和错误处理
- **要素关系管理**：配置要素间的依赖、互斥等关系
- **配置导入导出**：批量管理配置数据

## 2. 系统访问

### 2.1 访问地址
- **API接口地址**：http://localhost:8000/api/v1/config
- **API文档地址**：http://localhost:8000/docs

### 2.2 用户角色
- **系统管理员**：拥有所有配置权限
- **配置管理员**：负责要素配置和规则管理
- **业务用户**：查看配置和使用配置进行要素提取

## 3. 合同类型管理

### 3.1 查看合同类型
**接口**：`GET /api/v1/config/contract-types`

**功能**：获取系统中所有已配置的合同类型

**响应示例**：
```json
[
  {
    "id": 1,
    "type_code": "sales",
    "type_name": "销售合同",
    "type_description": "商品销售相关的合同",
    "is_active": true,
    "sort_order": 1
  }
]
```

### 3.2 创建合同类型
**接口**：`POST /api/v1/config/contract-types`

**请求示例**：
```json
{
  "type_code": "partnership",
  "type_name": "合伙协议",
  "type_description": "合伙关系相关协议",
  "sort_order": 10
}
```

### 3.3 合同类型识别配置
为每个合同类型配置识别规则，系统将根据这些规则自动识别合同类型。

**关键词识别配置**：
```json
{
  "recognition_type": "keyword",
  "recognition_config": {
    "keywords": [
      {"keyword": "合伙协议", "weight": 1.0},
      {"keyword": "合伙合同", "weight": 0.9}
    ]
  },
  "weight": 1.0
}
```

**模式匹配配置**：
```json
{
  "recognition_type": "pattern",
  "recognition_config": {
    "patterns": [
      {"pattern": ".*合伙.*协议.*", "weight": 0.8}
    ]
  },
  "weight": 0.8
}
```

## 4. 要素分类管理

### 4.1 查看要素分类
**接口**：`GET /api/v1/config/element-categories`

**功能**：获取要素分类的层级结构

### 4.2 分类层级结构
系统支持多级分类，典型的分类结构如下：

```
文档结构要素/
├── 合同标题
├── 合同编号
└── 文档元数据

当事方信息/
├── 基本信息/
│   ├── 甲方名称
│   └── 乙方名称
├── 联系信息/
│   ├── 联系电话
│   └── 联系地址
└── 法律信息/
    └── 法定代表人

专用条款要素/
├── 销售合同专用条款/
│   ├── 商品名称
│   └── 商品价格
├── 服务合同专用条款/
│   ├── 服务内容
│   └── 服务费用
└── 租赁合同专用条款/
    ├── 租赁物
    └── 租金
```

## 5. 要素模板配置

### 5.1 查看要素模板
**接口**：`GET /api/v1/config/element-templates`

**查询参数**：
- `contract_type`: 合同类型（可选）
- `category_id`: 分类ID（可选）
- `is_active`: 是否启用（可选）

### 5.2 创建要素模板
**接口**：`POST /api/v1/config/element-templates`

**请求示例**：
```json
{
  "element_name": "合伙期限",
  "element_code": "partnership_term",
  "element_display_name": "合伙期限",
  "element_description": "合伙协议的有效期限",
  "element_category_id": 5,
  "contract_type_id": 6,
  "data_type": "date",
  "is_required": true,
  "weight": 1.0,
  "sort_order": 10,
  "help_text": "请配置合伙期限的提取规则"
}
```

### 5.3 要素数据类型
系统支持以下数据类型：

- **text**: 文本类型，适用于一般文本要素
- **amount**: 金额类型，自动处理货币单位转换
- **date**: 日期类型，自动处理日期格式标准化
- **party_info**: 当事方信息，自动解析详细信息
- **clause**: 条款类型，自动分析条款结构

### 5.4 要素配置参数
```json
{
  "element_config": {
    "extraction_strategy": "multi_rule",
    "position_preference": "top",
    "currency_support": ["人民币", "美元"]
  },
  "validation_rules": {
    "min_length": 5,
    "max_length": 200,
    "required_keywords": ["期限", "时间"]
  }
}
```

## 6. 提取规则配置

### 6.1 查看提取规则
**接口**：`GET /api/v1/config/element-templates/{template_id}/extraction-rules`

### 6.2 创建提取规则
**接口**：`POST /api/v1/config/element-templates/{template_id}/extraction-rules`

### 6.3 正则表达式规则
**配置示例**：
```json
{
  "rule_name": "合伙期限提取",
  "rule_type": "regex",
  "rule_config": {
    "pattern": "合伙期限[：:]?\\s*(\\d{4}年\\d{1,2}月\\d{1,2}日)\\s*至\\s*(\\d{4}年\\d{1,2}月\\d{1,2}日)",
    "groups": [1, 2],
    "flags": ["MULTILINE"]
  },
  "priority": 10,
  "confidence_score": 0.9
}
```

**正则表达式规则说明**：
- `pattern`: 正则表达式模式
- `groups`: 提取的分组，可以是单个数字或数组
- `flags`: 正则表达式标志（MULTILINE, DOTALL, IGNORECASE）

### 6.4 关键词规则
**配置示例**：
```json
{
  "rule_name": "关键词后内容提取",
  "rule_type": "keyword",
  "rule_config": {
    "keywords": ["合伙期限", "协议期限"],
    "extract_after": true,
    "max_distance": 50
  },
  "priority": 5,
  "confidence_score": 0.7
}
```

**关键词规则说明**：
- `keywords`: 关键词列表
- `extract_after`: 是否提取关键词后的内容
- `max_distance`: 最大提取距离

### 6.5 AI提示词规则（预留）
```json
{
  "rule_name": "AI智能提取",
  "rule_type": "ai_prompt",
  "rule_config": {
    "prompt_template": "请从以下合同文本中提取合伙期限信息：{text}",
    "model": "qwen-3",
    "max_tokens": 100
  },
  "priority": 8,
  "confidence_score": 0.85
}
```

## 7. 验证规则配置

### 7.1 查看验证规则
**接口**：`GET /api/v1/config/element-templates/{template_id}/validation-rules`

### 7.2 格式验证规则
**配置示例**：
```json
{
  "validation_name": "期限格式验证",
  "validation_type": "format",
  "validation_config": {
    "min_length": 10,
    "max_length": 50,
    "pattern": "\\d{4}年\\d{1,2}月\\d{1,2}日"
  },
  "error_message": "合伙期限格式不正确，应为YYYY年MM月DD日格式",
  "is_blocking": true
}
```

### 7.3 范围验证规则
**配置示例**：
```json
{
  "validation_name": "期限合理性验证",
  "validation_type": "range",
  "validation_config": {
    "min_value": 30,
    "max_value": 18250,
    "unit": "days"
  },
  "error_message": "合伙期限必须在30天到50年之间",
  "is_blocking": true
}
```

### 7.4 业务规则验证
**配置示例**：
```json
{
  "validation_name": "期限关键词验证",
  "validation_type": "business_rule",
  "validation_config": {
    "required_keywords": ["年", "月", "日"],
    "match_any": false
  },
  "error_message": "期限信息必须包含年月日",
  "warning_message": "建议明确指定具体的年月日",
  "is_blocking": false
}
```

## 8. 要素关系配置

### 8.1 查看要素关系
**接口**：`GET /api/v1/config/element-relationships`

### 8.2 依赖关系配置
**配置示例**：
```json
{
  "relationship_name": "合伙期限依赖合伙方式",
  "relationship_type": "dependency",
  "source_elements": ["partnership_term"],
  "target_elements": ["partnership_type"],
  "relationship_config": {
    "rule": "if_target_missing_remove_source",
    "description": "如果没有合伙方式，则不应该有合伙期限"
  }
}
```

### 8.3 互斥关系配置
**配置示例**：
```json
{
  "relationship_name": "期限表述互斥",
  "relationship_type": "mutual_exclusion",
  "source_elements": ["partnership_term", "contract_duration"],
  "target_elements": [],
  "relationship_config": {
    "rule": "keep_highest_confidence",
    "description": "合伙期限和合同期限不应同时存在"
  }
}
```

### 8.4 组合关系配置
**配置示例**：
```json
{
  "relationship_name": "完整期限信息组合",
  "relationship_type": "composition",
  "source_elements": ["start_date", "end_date"],
  "target_elements": ["complete_term_info"],
  "relationship_config": {
    "rule": "combine_all",
    "format": "自{start_date}至{end_date}"
  }
}
```

## 9. 配置导入导出

### 9.1 导出配置
**接口**：`GET /api/v1/config/export`

**查询参数**：
- `contract_type`: 指定合同类型（可选，不指定则导出全部）

**功能**：导出指定合同类型或全部配置数据，包括：
- 合同类型配置
- 要素分类配置
- 要素模板配置
- 提取规则配置
- 验证规则配置
- 要素关系配置

### 9.2 导入配置
**接口**：`POST /api/v1/config/import`

**请求参数**：
```json
{
  "configuration_data": {
    "contract_types": [...],
    "element_categories": [...],
    "element_templates": [...],
    "extraction_rules": [...],
    "validation_rules": [...],
    "element_relationships": [...]
  },
  "import_mode": "merge",
  "validate_before_import": true,
  "create_backup": true
}
```

**导入模式说明**：
- `merge`: 合并模式，保留现有配置，添加新配置
- `replace`: 替换模式，完全替换现有配置
- `append`: 追加模式，只添加新配置，不修改现有配置

## 10. 缓存管理

### 10.1 重新加载缓存
**接口**：`POST /api/v1/config/reload-cache`

**功能**：手动重新加载所有配置缓存，在配置变更后确保立即生效

### 10.2 查看缓存状态
**接口**：`GET /api/v1/config/cache-status`

**响应示例**：
```json
{
  "last_updated": "2024-01-15T10:30:00Z",
  "cache_version": "1.0.0",
  "contract_types_count": 6,
  "element_categories_count": 12,
  "element_templates_count": 25
}
```

## 11. 最佳实践

### 11.1 配置规划
1. **分类设计**：合理设计要素分类层级，便于管理和查找
2. **命名规范**：使用清晰、一致的命名规范
3. **版本管理**：重要配置变更前创建版本备份
4. **测试验证**：新配置上线前充分测试

### 11.2 规则配置
1. **优先级设置**：合理设置提取规则的优先级
2. **置信度调优**：根据实际效果调整置信度
3. **规则组合**：使用多种规则类型提高提取准确率
4. **验证完整**：配置完整的验证规则确保数据质量

### 11.3 性能优化
1. **缓存利用**：合理利用系统缓存机制
2. **规则精简**：避免过于复杂的正则表达式
3. **分批处理**：大量配置变更时分批进行
4. **监控观察**：关注系统性能指标

### 11.4 故障处理
1. **备份恢复**：定期备份配置数据
2. **回滚机制**：配置问题时及时回滚
3. **日志查看**：通过日志分析问题原因
4. **逐步验证**：配置变更后逐步验证效果

## 12. 常见问题

### 12.1 配置不生效
**问题**：修改配置后系统仍使用旧配置
**解决**：调用缓存重新加载接口或等待缓存自动刷新

### 12.2 提取结果不准确
**问题**：要素提取结果不符合预期
**解决**：检查提取规则配置，调整正则表达式或优先级

### 12.3 验证规则过严
**问题**：验证规则导致大量有效数据被拒绝
**解决**：调整验证规则参数或将阻塞性验证改为警告

### 12.4 性能问题
**问题**：配置加载或要素提取速度慢
**解决**：优化正则表达式，检查数据库索引，调整缓存策略

这个用户手册为配置化要素管理系统的使用提供了详细的操作指导，帮助用户快速掌握系统的配置和使用方法。
