# 配置化要素管理系统 - 技术规范文档

## 1. 系统概述

### 1.1 系统定位
配置化要素管理系统是AI合同审核系统的核心基础模块，负责合同要素的配置化管理和动态提取。该系统实现了完全配置化的要素管理，支持无需修改代码即可配置新的合同类型和要素。

### 1.2 核心价值
- **配置化管理**：通过数据库配置实现要素模板、提取规则、验证规则的管理
- **热更新支持**：配置变更实时生效，无需重启系统
- **高扩展性**：支持新合同类型和要素的快速配置
- **智能提取**：基于多种提取方式的智能要素提取引擎

## 2. 技术架构

### 2.1 系统分层架构
```
┌─────────────────────────────────────────────────────────┐
│                    API接口层                             │
│  config_api.py - RESTful API接口                        │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   业务逻辑层                             │
│  config_manager.py - 配置管理器                         │
│  element_extractor.py - 要素提取引擎                    │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   数据模型层                             │
│  config_models.py - Pydantic数据模型                    │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   数据存储层                             │
│  database_schema.sql - PostgreSQL数据库表结构           │
│  sample_data.sql - 示例配置数据                         │
└─────────────────────────────────────────────────────────┘
```

### 2.2 核心组件说明

#### 2.2.1 配置管理器 (ConfigurationManager)
- **功能**：配置的加载、缓存、热更新管理
- **特性**：
  - TTL缓存机制，自动刷新配置
  - 并发安全的配置更新
  - 数据库不可用时的降级机制
  - 配置变更的智能缓存失效

#### 2.2.2 要素提取引擎 (ConfigurableElementExtractor)
- **功能**：基于配置的动态要素提取
- **特性**：
  - 多种提取方式：正则表达式、关键词、AI提示词
  - 要素关系处理：依赖、互斥、组合、计算
  - 提取结果验证和质量评估
  - 可扩展的处理器架构

#### 2.2.3 数据模型层 (config_models.py)
- **功能**：系统数据结构的标准化定义
- **特性**：
  - 基于Pydantic的类型安全
  - 完整的数据验证规则
  - 请求/响应模型的标准化
  - 配置导入导出模型

#### 2.2.4 API接口层 (config_api.py)
- **功能**：配置管理的RESTful API接口
- **特性**：
  - 完整的CRUD操作
  - 配置导入导出功能
  - 缓存管理和状态查询
  - 标准化的错误处理

## 3. 数据库设计

### 3.1 核心表结构

#### 3.1.1 合同类型管理
- **contract_types**: 合同类型定义
- **contract_type_recognition**: 合同类型识别配置

#### 3.1.2 要素分类管理
- **element_categories**: 要素分类层级结构

#### 3.1.3 要素配置管理
- **contract_element_templates**: 要素模板核心配置
- **element_extraction_rules**: 要素提取规则
- **element_validation_rules**: 要素验证规则
- **element_relationships**: 要素关系配置

#### 3.1.4 系统管理
- **configuration_versions**: 配置版本管理
- **configuration_change_logs**: 配置变更日志
- **element_extraction_results**: 运行时提取结果

### 3.2 数据库特性
- **完整索引**：优化查询性能的索引设计
- **触发器**：自动更新时间戳、分类路径、变更日志
- **约束检查**：数据完整性和一致性保证
- **JSONB支持**：灵活的配置数据存储

## 4. 配置化设计

### 4.1 要素模板配置
```json
{
  "element_name": "合同标题",
  "element_code": "contract_title",
  "data_type": "text",
  "is_required": true,
  "element_config": {
    "extraction_strategy": "multi_rule",
    "position_preference": "top"
  },
  "validation_rules": {
    "min_length": 5,
    "max_length": 200,
    "required_keywords": ["合同", "协议"]
  }
}
```

### 4.2 提取规则配置
```json
{
  "rule_name": "标题关键词匹配",
  "rule_type": "regex",
  "rule_config": {
    "pattern": "^(.{5,100}?)(合同|协议|契约)",
    "group": 1,
    "flags": ["MULTILINE"]
  },
  "priority": 10,
  "confidence_score": 0.9
}
```

### 4.3 验证规则配置
```json
{
  "validation_name": "标题长度验证",
  "validation_type": "format",
  "validation_config": {
    "min_length": 5,
    "max_length": 200
  },
  "error_message": "合同标题长度必须在5-200字符之间",
  "is_blocking": true
}
```

### 4.4 要素关系配置
```json
{
  "relationship_name": "商品价格依赖商品名称",
  "relationship_type": "dependency",
  "source_elements": ["product_price"],
  "target_elements": ["product_name"],
  "relationship_config": {
    "rule": "if_target_missing_remove_source"
  }
}
```

## 5. API接口规范

### 5.1 要素模板管理
- `GET /api/v1/config/element-templates` - 获取要素模板列表
- `GET /api/v1/config/element-templates/{element_code}` - 获取指定要素模板
- `POST /api/v1/config/element-templates` - 创建要素模板
- `PUT /api/v1/config/element-templates/{template_id}` - 更新要素模板
- `DELETE /api/v1/config/element-templates/{template_id}` - 删除要素模板

### 5.2 提取规则管理
- `GET /api/v1/config/element-templates/{template_id}/extraction-rules` - 获取提取规则
- `POST /api/v1/config/element-templates/{template_id}/extraction-rules` - 创建提取规则

### 5.3 配置导入导出
- `GET /api/v1/config/export` - 导出配置
- `POST /api/v1/config/import` - 导入配置

### 5.4 缓存管理
- `POST /api/v1/config/reload-cache` - 重新加载配置缓存
- `GET /api/v1/config/cache-status` - 获取缓存状态

## 6. 性能优化

### 6.1 缓存策略
- **多层级缓存**：配置数据的分层缓存
- **TTL机制**：自动刷新过期配置
- **智能失效**：配置变更时的精确缓存失效

### 6.2 数据库优化
- **索引优化**：针对查询模式的索引设计
- **查询优化**：减少数据库查询次数
- **连接池**：数据库连接的高效管理

### 6.3 并发处理
- **异步架构**：基于asyncio的异步处理
- **并发安全**：配置更新的线程安全保证
- **任务队列**：大批量配置操作的队列处理

## 7. 扩展性设计

### 7.1 新要素类型支持
- **处理器注册**：可扩展的要素处理器架构
- **类型定义**：新数据类型的动态支持
- **验证扩展**：自定义验证规则的插件机制

### 7.2 新提取方式支持
- **规则引擎**：可扩展的提取规则引擎
- **AI集成**：支持多种AI模型的集成
- **外部API**：支持外部服务的集成

### 7.3 新合同类型支持
- **类型继承**：合同类型的继承机制
- **配置模板**：快速配置新合同类型的模板
- **批量导入**：配置的批量导入导出

## 8. 安全性设计

### 8.1 数据安全
- **输入验证**：严格的输入数据验证
- **SQL注入防护**：参数化查询防止注入
- **权限控制**：基于角色的访问控制

### 8.2 配置安全
- **变更审计**：完整的配置变更日志
- **版本控制**：配置的版本管理和回滚
- **备份恢复**：配置数据的备份和恢复

## 9. 监控和维护

### 9.1 系统监控
- **性能监控**：配置加载和提取性能监控
- **错误监控**：系统错误和异常监控
- **使用统计**：配置使用情况统计

### 9.2 运维支持
- **健康检查**：系统健康状态检查
- **日志管理**：结构化日志记录和管理
- **故障恢复**：自动故障检测和恢复机制

## 10. 部署指南

### 10.1 环境要求
- **Python**: 3.8+
- **数据库**: PostgreSQL 12+
- **依赖**: FastAPI, Pydantic, asyncpg

### 10.2 部署步骤
1. 数据库初始化：执行database_schema.sql
2. 示例数据导入：执行sample_data.sql
3. 配置管理器初始化
4. API服务启动

### 10.3 配置管理
- 通过API接口进行配置管理
- 支持配置的热更新
- 提供配置导入导出工具

这个技术规范文档为配置化要素管理系统的开发、部署和维护提供了完整的技术指导。
