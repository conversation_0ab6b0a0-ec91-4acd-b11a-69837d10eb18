"""
配置化要素提取引擎
基于配置管理器的动态要素提取系统
"""

import re
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

from config_manager import ConfigurationManager
from config_models import (
    ElementTemplateModel, ExtractionRuleModel, ValidationRuleModel,
    ElementExtractionResultModel
)

logger = logging.getLogger(__name__)

@dataclass
class ExtractionContext:
    """提取上下文"""
    document_id: str
    full_text: str
    paragraphs: List[Dict[str, Any]]
    contract_type: str
    metadata: Dict[str, Any]

class ConfigurableElementExtractor:
    """配置化要素提取器"""
    
    def __init__(self, config_manager: ConfigurationManager):
        """
        初始化要素提取器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.processors = {
            'text': self._process_text_element,
            'amount': self._process_amount_element,
            'date': self._process_date_element,
            'party_info': self._process_party_info_element,
            'clause': self._process_clause_element
        }
        
    async def extract_contract_elements(self, context: ExtractionContext) -> Dict[str, Any]:
        """
        提取合同要素
        
        Args:
            context: 提取上下文
            
        Returns:
            提取结果字典
        """
        logger.info(f"开始提取合同要素，文档ID: {context.document_id}, 合同类型: {context.contract_type}")
        
        start_time = datetime.now()
        
        # 获取要素模板配置
        element_templates = await self.config_manager.get_element_templates(context.contract_type)
        
        if not element_templates:
            logger.warning(f"未找到合同类型 {context.contract_type} 的要素模板配置")
            return self._create_empty_result(context)
        
        # 按分类组织提取结果
        extracted_elements = {}
        extraction_results = []
        
        # 逐个处理要素模板
        for template in element_templates:
            try:
                logger.debug(f"处理要素: {template.element_name} ({template.element_code})")
                
                # 获取提取规则
                extraction_rules = await self.config_manager.get_extraction_rules(template.id)
                
                if not extraction_rules:
                    logger.warning(f"要素 {template.element_name} 没有配置提取规则")
                    continue
                
                # 执行要素提取
                element_results = await self._extract_single_element(
                    context, template, extraction_rules
                )
                
                if element_results:
                    # 按分类组织结果
                    category = await self._get_element_category_path(template.element_category_id)
                    self._organize_results_by_category(extracted_elements, category, template.element_name, element_results)
                    
                    # 记录提取结果
                    extraction_results.extend(element_results)
                    
                    logger.debug(f"要素 {template.element_name} 提取到 {len(element_results)} 个结果")
                
            except Exception as e:
                logger.error(f"提取要素 {template.element_name} 时发生错误: {e}")
                continue
        
        # 处理要素关系
        extracted_elements = await self._process_element_relationships(
            extracted_elements, context.contract_type
        )
        
        # 验证提取结果
        validated_results = await self._validate_extraction_results(
            extraction_results, element_templates
        )
        
        processing_duration = (datetime.now() - start_time).total_seconds()
        
        result = {
            'document_id': context.document_id,
            'contract_type': context.contract_type,
            'extracted_elements': extracted_elements,
            'extraction_results': validated_results,
            'statistics': {
                'total_templates': len(element_templates),
                'successful_extractions': len([r for r in validated_results if r.extracted_value]),
                'processing_duration': processing_duration,
                'extraction_timestamp': datetime.now().isoformat()
            }
        }
        
        logger.info(f"要素提取完成，耗时: {processing_duration:.2f}秒，成功提取: {result['statistics']['successful_extractions']} 个要素")
        
        return result
    
    async def _extract_single_element(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        extraction_rules: List[ExtractionRuleModel]
    ) -> List[ElementExtractionResultModel]:
        """提取单个要素"""
        
        results = []
        
        # 获取对应的处理器
        processor = self.processors.get(template.data_type, self.processors['text'])
        
        # 按优先级处理提取规则
        for rule in extraction_rules:
            try:
                rule_results = await processor(context, template, rule)
                
                if rule_results:
                    results.extend(rule_results)
                    
                    # 如果是唯一要素且已找到结果，停止处理后续规则
                    if template.is_unique and results:
                        break
                        
            except Exception as e:
                logger.error(f"执行提取规则 {rule.rule_name} 时发生错误: {e}")
                continue
        
        # 去重和排序
        if results:
            results = self._deduplicate_results(results)
            results.sort(key=lambda x: x.confidence_score or 0, reverse=True)
            
            # 如果是唯一要素，只保留置信度最高的结果
            if template.is_unique and len(results) > 1:
                results = [results[0]]
        
        return results
    
    async def _process_text_element(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """处理文本类型要素"""
        
        results = []
        
        if rule.rule_type == 'regex':
            results = await self._extract_by_regex(context, template, rule)
        elif rule.rule_type == 'keyword':
            results = await self._extract_by_keyword(context, template, rule)
        elif rule.rule_type == 'ai_prompt':
            results = await self._extract_by_ai_prompt(context, template, rule)
        
        return results
    
    async def _process_amount_element(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """处理金额类型要素"""
        
        results = await self._extract_by_regex(context, template, rule)
        
        # 对金额结果进行特殊处理
        for result in results:
            if result.extracted_value:
                result.extracted_value = await self._normalize_amount(result.extracted_value, rule.rule_config)
        
        return results
    
    async def _process_date_element(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """处理日期类型要素"""
        
        results = await self._extract_by_regex(context, template, rule)
        
        # 对日期结果进行特殊处理
        for result in results:
            if result.extracted_value:
                result.extracted_value = await self._normalize_date(result.extracted_value)
        
        return results
    
    async def _process_party_info_element(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """处理当事方信息要素"""
        
        results = await self._extract_by_regex(context, template, rule)
        
        # 对当事方信息进行特殊处理
        for result in results:
            if result.extracted_value:
                result.verification_details = await self._parse_party_details(result.extracted_value)
        
        return results
    
    async def _process_clause_element(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """处理条款类型要素"""
        
        results = await self._extract_by_regex(context, template, rule)
        
        # 对条款结果进行特殊处理
        for result in results:
            if result.extracted_value:
                result.verification_details = await self._analyze_clause_structure(result.extracted_value)
        
        return results
    
    async def _extract_by_regex(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """基于正则表达式提取"""
        
        results = []
        config = rule.rule_config
        
        pattern = config.get('pattern', '')
        if not pattern:
            return results
        
        flags = 0
        if 'MULTILINE' in config.get('flags', []):
            flags |= re.MULTILINE
        if 'DOTALL' in config.get('flags', []):
            flags |= re.DOTALL
        if 'IGNORECASE' in config.get('flags', []):
            flags |= re.IGNORECASE
        
        try:
            matches = re.finditer(pattern, context.full_text, flags)
            
            for match in matches:
                # 确定提取的值
                group_index = config.get('group', 0)
                if isinstance(group_index, list):
                    # 多个分组
                    extracted_value = ' '.join([match.group(i) for i in group_index if match.group(i)])
                else:
                    # 单个分组
                    extracted_value = match.group(group_index) if len(match.groups()) >= group_index else match.group(0)
                
                if extracted_value and extracted_value.strip():
                    result = ElementExtractionResultModel(
                        document_id=context.document_id,
                        element_template_id=template.id,
                        extraction_rule_id=rule.id,
                        extracted_value=extracted_value.strip(),
                        confidence_score=rule.confidence_score,
                        source_position={
                            'start': match.start(),
                            'end': match.end(),
                            'paragraph_index': self._find_paragraph_index(context.paragraphs, match.start())
                        },
                        verification_status='regex_extracted',
                        created_at=datetime.now()
                    )
                    results.append(result)
        
        except re.error as e:
            logger.error(f"正则表达式错误: {pattern}, 错误: {e}")
        
        return results
    
    async def _extract_by_keyword(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """基于关键词提取"""
        
        results = []
        config = rule.rule_config
        
        keywords = config.get('keywords', [])
        if not keywords:
            return results
        
        for keyword in keywords:
            if keyword in context.full_text:
                # 简单的关键词后内容提取
                keyword_pos = context.full_text.find(keyword)
                extract_after = config.get('extract_after', True)
                max_distance = config.get('max_distance', 50)
                
                if extract_after:
                    start_pos = keyword_pos + len(keyword)
                    end_pos = min(start_pos + max_distance, len(context.full_text))
                    extracted_value = context.full_text[start_pos:end_pos].strip()
                else:
                    start_pos = max(0, keyword_pos - max_distance)
                    extracted_value = context.full_text[start_pos:keyword_pos].strip()
                
                if extracted_value:
                    result = ElementExtractionResultModel(
                        document_id=context.document_id,
                        element_template_id=template.id,
                        extraction_rule_id=rule.id,
                        extracted_value=extracted_value,
                        confidence_score=rule.confidence_score,
                        source_position={
                            'start': keyword_pos,
                            'end': keyword_pos + len(extracted_value),
                            'paragraph_index': self._find_paragraph_index(context.paragraphs, keyword_pos)
                        },
                        verification_status='keyword_extracted',
                        created_at=datetime.now()
                    )
                    results.append(result)
        
        return results
    
    async def _extract_by_ai_prompt(
        self, 
        context: ExtractionContext, 
        template: ElementTemplateModel, 
        rule: ExtractionRuleModel
    ) -> List[ElementExtractionResultModel]:
        """基于AI提示词提取（预留接口）"""
        
        # 这里可以集成AI服务进行智能提取
        # 目前返回空结果
        logger.info(f"AI提取功能待实现: {template.element_name}")
        return []

    def _find_paragraph_index(self, paragraphs: List[Dict], position: int) -> int:
        """查找位置对应的段落索引"""
        current_pos = 0
        for i, paragraph in enumerate(paragraphs):
            text_length = len(paragraph.get('text', ''))
            if current_pos <= position < current_pos + text_length:
                return i
            current_pos += text_length + 1  # +1 for newline
        return len(paragraphs) - 1

    def _deduplicate_results(self, results: List[ElementExtractionResultModel]) -> List[ElementExtractionResultModel]:
        """去除重复的提取结果"""
        seen_values = set()
        unique_results = []

        for result in results:
            value_key = (result.extracted_value, result.source_position.get('start', 0))
            if value_key not in seen_values:
                seen_values.add(value_key)
                unique_results.append(result)

        return unique_results

    async def _normalize_amount(self, amount_text: str, config: Dict[str, Any]) -> str:
        """标准化金额格式"""
        import re

        # 移除逗号和空格
        normalized = re.sub(r'[,，\s]', '', amount_text)

        # 处理单位转换
        if '万元' in amount_text:
            number_match = re.search(r'([\d.]+)', normalized)
            if number_match:
                number = float(number_match.group(1))
                normalized = f"{number * 10000:.2f}元"

        return normalized

    async def _normalize_date(self, date_text: str) -> str:
        """标准化日期格式"""
        import re

        # 中文日期格式转换
        match = re.search(r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', date_text)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        # 数字日期格式
        match = re.search(r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})', date_text)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"

        return date_text

    async def _parse_party_details(self, party_text: str) -> Dict[str, Any]:
        """解析当事方详细信息"""
        import re

        details = {
            'name': '',
            'address': '',
            'contact': '',
            'legal_representative': ''
        }

        # 提取名称（第一部分）
        name_match = re.search(r'^([^，,；;（(]+)', party_text)
        if name_match:
            details['name'] = name_match.group(1).strip()

        # 提取地址
        address_match = re.search(r'地址[：:]?\s*([^，,；;电话手机传真邮箱]+)', party_text)
        if address_match:
            details['address'] = address_match.group(1).strip()

        # 提取联系方式
        contact_match = re.search(r'电话[：:]?\s*([\d\-\s]+)', party_text)
        if contact_match:
            details['contact'] = contact_match.group(1).strip()

        # 提取法定代表人
        legal_rep_match = re.search(r'法定代表人[：:]?\s*([^，,；;]+)', party_text)
        if legal_rep_match:
            details['legal_representative'] = legal_rep_match.group(1).strip()

        return details

    async def _analyze_clause_structure(self, clause_text: str) -> Dict[str, Any]:
        """分析条款结构"""
        import re

        structure = {
            'clause_number': '',
            'clause_level': 'general',
            'clause_type': 'general',
            'content': clause_text
        }

        # 识别条款编号
        number_patterns = [
            r'^第([一二三四五六七八九十\d]+)条',
            r'^第([一二三四五六七八九十\d]+)款',
            r'^([一二三四五六七八九十]+)、',
            r'^(\d+)\.'
        ]

        for pattern in number_patterns:
            match = re.search(pattern, clause_text)
            if match:
                structure['clause_number'] = match.group(1)
                if '条' in pattern:
                    structure['clause_level'] = 'main_clause'
                elif '款' in pattern:
                    structure['clause_level'] = 'sub_clause'
                break

        # 识别条款类型（基于关键词）
        if any(keyword in clause_text for keyword in ['争议', '纠纷', '仲裁', '诉讼']):
            structure['clause_type'] = 'dispute_resolution'
        elif any(keyword in clause_text for keyword in ['违约', '责任', '赔偿']):
            structure['clause_type'] = 'breach_liability'
        elif any(keyword in clause_text for keyword in ['价格', '费用', '金额', '支付']):
            structure['clause_type'] = 'payment'

        return structure

    async def _get_element_category_path(self, category_id: int) -> str:
        """获取要素分类路径"""
        categories = await self.config_manager.get_element_categories()

        for category in categories.values():
            if category.id == category_id:
                return category.category_path or category.category_code

        return 'unknown'

    def _organize_results_by_category(self, extracted_elements: Dict, category_path: str, element_name: str, results: List):
        """按分类组织提取结果"""
        path_parts = category_path.split('/')
        current_level = extracted_elements

        # 创建分类层级结构
        for part in path_parts:
            if part not in current_level:
                current_level[part] = {}
            current_level = current_level[part]

        # 添加要素结果
        current_level[element_name] = [
            {
                'value': result.extracted_value,
                'confidence': result.confidence_score,
                'source_position': result.source_position,
                'verification_status': result.verification_status,
                'verification_details': result.verification_details
            }
            for result in results
        ]

    async def _process_element_relationships(self, extracted_elements: Dict, contract_type: str) -> Dict:
        """处理要素关系"""
        relationships = await self.config_manager.get_element_relationships(contract_type)

        for relationship in relationships:
            try:
                if relationship.relationship_type == 'dependency':
                    await self._process_dependency_relationship(extracted_elements, relationship)
                elif relationship.relationship_type == 'mutual_exclusion':
                    await self._process_mutual_exclusion_relationship(extracted_elements, relationship)
                elif relationship.relationship_type == 'composition':
                    await self._process_composition_relationship(extracted_elements, relationship)
                elif relationship.relationship_type == 'calculation':
                    await self._process_calculation_relationship(extracted_elements, relationship)
            except Exception as e:
                logger.error(f"处理要素关系 {relationship.relationship_name} 时发生错误: {e}")

        return extracted_elements

    async def _process_dependency_relationship(self, elements: Dict, relationship):
        """处理依赖关系"""
        # 简化实现：如果依赖的要素不存在，移除当前要素
        pass

    async def _process_mutual_exclusion_relationship(self, elements: Dict, relationship):
        """处理互斥关系"""
        # 简化实现：保留置信度最高的要素
        pass

    async def _process_composition_relationship(self, elements: Dict, relationship):
        """处理组合关系"""
        # 简化实现：将多个要素组合成一个
        pass

    async def _process_calculation_relationship(self, elements: Dict, relationship):
        """处理计算关系"""
        # 简化实现：根据其他要素计算新要素
        pass

    async def _validate_extraction_results(
        self,
        results: List[ElementExtractionResultModel],
        templates: List[ElementTemplateModel]
    ) -> List[ElementExtractionResultModel]:
        """验证提取结果"""
        validated_results = []

        for result in results:
            # 查找对应的模板
            template = next((t for t in templates if t.id == result.element_template_id), None)
            if not template:
                continue

            # 获取验证规则
            validation_rules = await self.config_manager.get_validation_rules(template.id)

            # 执行验证
            validation_passed = True
            validation_errors = []

            for rule in validation_rules:
                try:
                    is_valid, error_msg = await self._validate_single_rule(result.extracted_value, rule)
                    if not is_valid:
                        validation_passed = False
                        validation_errors.append(error_msg)

                        # 如果是阻塞性验证失败，跳过该结果
                        if rule.is_blocking:
                            break
                except Exception as e:
                    logger.error(f"验证规则 {rule.validation_name} 执行失败: {e}")

            # 更新验证状态
            if validation_passed:
                result.verification_status = 'validated'
                result.is_validated = True
            else:
                result.verification_status = 'validation_failed'
                result.verification_details = {
                    'errors': validation_errors
                }

            validated_results.append(result)

        return validated_results

    async def _validate_single_rule(self, value: str, rule: ValidationRuleModel) -> Tuple[bool, str]:
        """执行单个验证规则"""
        config = rule.validation_config

        if rule.validation_type == 'format':
            # 格式验证
            min_length = config.get('min_length', 0)
            max_length = config.get('max_length', float('inf'))

            if not (min_length <= len(value) <= max_length):
                return False, rule.error_message or f"长度必须在{min_length}-{max_length}之间"

        elif rule.validation_type == 'range':
            # 范围验证
            try:
                numeric_value = float(value)
                min_value = config.get('min_value', float('-inf'))
                max_value = config.get('max_value', float('inf'))

                if not (min_value <= numeric_value <= max_value):
                    return False, rule.error_message or f"值必须在{min_value}-{max_value}之间"
            except ValueError:
                return False, rule.error_message or "必须是数字"

        elif rule.validation_type == 'business_rule':
            # 业务规则验证
            required_keywords = config.get('required_keywords', [])
            match_any = config.get('match_any', False)

            if required_keywords:
                if match_any:
                    if not any(keyword in value for keyword in required_keywords):
                        return False, rule.error_message or f"必须包含以下关键词之一: {required_keywords}"
                else:
                    if not all(keyword in value for keyword in required_keywords):
                        return False, rule.error_message or f"必须包含所有关键词: {required_keywords}"

        return True, ""

    def _create_empty_result(self, context: ExtractionContext) -> Dict[str, Any]:
        """创建空的提取结果"""
        return {
            'document_id': context.document_id,
            'contract_type': context.contract_type,
            'extracted_elements': {},
            'extraction_results': [],
            'statistics': {
                'total_templates': 0,
                'successful_extractions': 0,
                'processing_duration': 0,
                'extraction_timestamp': datetime.now().isoformat()
            }
        }
