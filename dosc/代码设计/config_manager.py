"""
配置化要素管理系统 - 配置管理器
负责要素配置的加载、缓存和管理
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass
from config_models import (
    ContractTypeModel, ElementTemplateModel, ExtractionRuleModel,
    ValidationRuleModel, ElementRelationshipModel, ElementCategoryModel
)

logger = logging.getLogger(__name__)

@dataclass
class ConfigCache:
    """配置缓存数据结构"""
    contract_types: Dict[str, ContractTypeModel] = None
    element_categories: Dict[str, ElementCategoryModel] = None
    element_templates: Dict[str, List[ElementTemplateModel]] = None  # 按合同类型分组
    extraction_rules: Dict[int, List[ExtractionRuleModel]] = None    # 按模板ID分组
    validation_rules: Dict[int, List[ValidationRuleModel]] = None    # 按模板ID分组
    element_relationships: Dict[str, List[ElementRelationshipModel]] = None  # 按合同类型分组
    last_updated: datetime = None
    cache_version: str = "1.0.0"

class ConfigurationManager:
    """配置管理器 - 负责配置的加载、缓存和热更新"""
    
    def __init__(self, db_connection=None, cache_ttl: int = 300):
        """
        初始化配置管理器
        
        Args:
            db_connection: 数据库连接
            cache_ttl: 缓存过期时间（秒）
        """
        self.db = db_connection
        self.cache_ttl = cache_ttl
        self.config_cache = ConfigCache()
        self._cache_lock = asyncio.Lock()
        self._last_cache_update = None
        
    async def initialize(self):
        """初始化配置管理器"""
        logger.info("初始化配置管理器...")
        await self.reload_all_configurations()
        logger.info("配置管理器初始化完成")
    
    async def reload_all_configurations(self):
        """重新加载所有配置"""
        async with self._cache_lock:
            logger.info("开始重新加载所有配置...")
            
            # 加载合同类型配置
            self.config_cache.contract_types = await self._load_contract_types()
            
            # 加载要素分类配置
            self.config_cache.element_categories = await self._load_element_categories()
            
            # 加载要素模板配置
            self.config_cache.element_templates = await self._load_element_templates()
            
            # 加载提取规则配置
            self.config_cache.extraction_rules = await self._load_extraction_rules()
            
            # 加载验证规则配置
            self.config_cache.validation_rules = await self._load_validation_rules()
            
            # 加载要素关系配置
            self.config_cache.element_relationships = await self._load_element_relationships()
            
            self.config_cache.last_updated = datetime.now()
            self._last_cache_update = datetime.now()
            
            logger.info("所有配置重新加载完成")
    
    async def get_contract_types(self) -> Dict[str, ContractTypeModel]:
        """获取所有合同类型配置"""
        await self._ensure_cache_fresh()
        return self.config_cache.contract_types or {}
    
    async def get_contract_type(self, type_code: str) -> Optional[ContractTypeModel]:
        """获取指定合同类型配置"""
        contract_types = await self.get_contract_types()
        return contract_types.get(type_code)
    
    async def get_element_templates(self, contract_type: str = None) -> List[ElementTemplateModel]:
        """
        获取要素模板配置
        
        Args:
            contract_type: 合同类型，None表示获取通用模板
            
        Returns:
            要素模板列表
        """
        await self._ensure_cache_fresh()
        
        if not self.config_cache.element_templates:
            return []
        
        # 获取通用模板
        templates = self.config_cache.element_templates.get('general', []).copy()
        
        # 如果指定了合同类型，添加专用模板
        if contract_type and contract_type != 'general':
            specific_templates = self.config_cache.element_templates.get(contract_type, [])
            templates.extend(specific_templates)
        
        # 按排序和权重排序
        templates.sort(key=lambda x: (x.sort_order, -x.weight, x.element_name))
        
        return templates
    
    async def get_element_template(self, element_code: str, contract_type: str = None) -> Optional[ElementTemplateModel]:
        """获取指定要素模板"""
        templates = await self.get_element_templates(contract_type)
        
        for template in templates:
            if template.element_code == element_code:
                return template
        
        return None
    
    async def get_extraction_rules(self, element_template_id: int) -> List[ExtractionRuleModel]:
        """获取指定要素模板的提取规则"""
        await self._ensure_cache_fresh()
        
        if not self.config_cache.extraction_rules:
            return []
        
        rules = self.config_cache.extraction_rules.get(element_template_id, [])
        
        # 按优先级和置信度排序
        rules.sort(key=lambda x: (-x.priority, -x.confidence_score))
        
        return [rule for rule in rules if rule.is_active]
    
    async def get_validation_rules(self, element_template_id: int) -> List[ValidationRuleModel]:
        """获取指定要素模板的验证规则"""
        await self._ensure_cache_fresh()
        
        if not self.config_cache.validation_rules:
            return []
        
        rules = self.config_cache.validation_rules.get(element_template_id, [])
        return [rule for rule in rules if rule.is_active]
    
    async def get_element_relationships(self, contract_type: str = None) -> List[ElementRelationshipModel]:
        """获取要素关系配置"""
        await self._ensure_cache_fresh()
        
        if not self.config_cache.element_relationships:
            return []
        
        # 获取通用关系
        relationships = self.config_cache.element_relationships.get('general', []).copy()
        
        # 如果指定了合同类型，添加专用关系
        if contract_type and contract_type != 'general':
            specific_relationships = self.config_cache.element_relationships.get(contract_type, [])
            relationships.extend(specific_relationships)
        
        return [rel for rel in relationships if rel.is_active]
    
    async def get_element_categories(self) -> Dict[str, ElementCategoryModel]:
        """获取要素分类配置"""
        await self._ensure_cache_fresh()
        return self.config_cache.element_categories or {}
    
    async def get_category_hierarchy(self, category_code: str) -> List[ElementCategoryModel]:
        """获取分类层级结构"""
        categories = await self.get_element_categories()
        hierarchy = []
        
        current_category = categories.get(category_code)
        while current_category:
            hierarchy.insert(0, current_category)
            if current_category.parent_category_id:
                # 查找父分类
                parent_category = None
                for cat in categories.values():
                    if cat.id == current_category.parent_category_id:
                        parent_category = cat
                        break
                current_category = parent_category
            else:
                break
        
        return hierarchy
    
    async def invalidate_cache(self):
        """使缓存失效，强制下次访问时重新加载"""
        async with self._cache_lock:
            self._last_cache_update = None
            logger.info("配置缓存已失效")
    
    async def _ensure_cache_fresh(self):
        """确保缓存是新鲜的"""
        if (self._last_cache_update is None or 
            (datetime.now() - self._last_cache_update).seconds > self.cache_ttl):
            await self.reload_all_configurations()
    
    async def _load_contract_types(self) -> Dict[str, ContractTypeModel]:
        """从数据库加载合同类型配置"""
        if not self.db:
            return self._get_default_contract_types()
        
        try:
            query = """
            SELECT id, type_code, type_name, type_description, parent_type_id,
                   is_active, sort_order, created_at, updated_at, created_by, updated_by
            FROM contract_types 
            WHERE is_active = TRUE 
            ORDER BY sort_order, type_name
            """
            
            rows = await self._execute_query(query)
            contract_types = {}
            
            for row in rows:
                contract_type = ContractTypeModel(
                    id=row['id'],
                    type_code=row['type_code'],
                    type_name=row['type_name'],
                    type_description=row['type_description'],
                    parent_type_id=row['parent_type_id'],
                    is_active=row['is_active'],
                    sort_order=row['sort_order'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    created_by=row['created_by'],
                    updated_by=row['updated_by']
                )
                contract_types[contract_type.type_code] = contract_type
            
            logger.info(f"加载了 {len(contract_types)} 个合同类型配置")
            return contract_types
            
        except Exception as e:
            logger.error(f"加载合同类型配置失败: {e}")
            return self._get_default_contract_types()
    
    async def _load_element_categories(self) -> Dict[str, ElementCategoryModel]:
        """从数据库加载要素分类配置"""
        if not self.db:
            return self._get_default_element_categories()
        
        try:
            query = """
            SELECT id, category_code, category_name, category_description,
                   parent_category_id, category_level, category_path,
                   is_active, sort_order, created_at, updated_at, created_by, updated_by
            FROM element_categories 
            WHERE is_active = TRUE 
            ORDER BY category_level, sort_order, category_name
            """
            
            rows = await self._execute_query(query)
            categories = {}
            
            for row in rows:
                category = ElementCategoryModel(
                    id=row['id'],
                    category_code=row['category_code'],
                    category_name=row['category_name'],
                    category_description=row['category_description'],
                    parent_category_id=row['parent_category_id'],
                    category_level=row['category_level'],
                    category_path=row['category_path'],
                    is_active=row['is_active'],
                    sort_order=row['sort_order'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    created_by=row['created_by'],
                    updated_by=row['updated_by']
                )
                categories[category.category_code] = category
            
            logger.info(f"加载了 {len(categories)} 个要素分类配置")
            return categories
            
        except Exception as e:
            logger.error(f"加载要素分类配置失败: {e}")
            return self._get_default_element_categories()
    
    async def _load_element_templates(self) -> Dict[str, List[ElementTemplateModel]]:
        """从数据库加载要素模板配置"""
        if not self.db:
            return self._get_default_element_templates()
        
        try:
            query = """
            SELECT et.id, et.element_name, et.element_code, et.element_display_name,
                   et.element_description, et.element_category_id, et.contract_type_id,
                   et.data_type, et.is_required, et.is_unique, et.weight, et.sort_order,
                   et.element_config, et.validation_rules, et.default_value, et.help_text,
                   et.is_active, et.version, et.created_at, et.updated_at, et.created_by, et.updated_by,
                   ct.type_code
            FROM contract_element_templates et
            LEFT JOIN contract_types ct ON et.contract_type_id = ct.id
            WHERE et.is_active = TRUE
            ORDER BY et.sort_order, et.element_name
            """
            
            rows = await self._execute_query(query)
            templates_by_type = {}
            
            for row in rows:
                template = ElementTemplateModel(
                    id=row['id'],
                    element_name=row['element_name'],
                    element_code=row['element_code'],
                    element_display_name=row['element_display_name'],
                    element_description=row['element_description'],
                    element_category_id=row['element_category_id'],
                    contract_type_id=row['contract_type_id'],
                    data_type=row['data_type'],
                    is_required=row['is_required'],
                    is_unique=row['is_unique'],
                    weight=row['weight'],
                    sort_order=row['sort_order'],
                    element_config=row['element_config'],
                    validation_rules=row['validation_rules'],
                    default_value=row['default_value'],
                    help_text=row['help_text'],
                    is_active=row['is_active'],
                    version=row['version'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    created_by=row['created_by'],
                    updated_by=row['updated_by']
                )
                
                # 按合同类型分组
                contract_type = row['type_code'] or 'general'
                if contract_type not in templates_by_type:
                    templates_by_type[contract_type] = []
                templates_by_type[contract_type].append(template)
            
            logger.info(f"加载了 {sum(len(templates) for templates in templates_by_type.values())} 个要素模板配置")
            return templates_by_type
            
        except Exception as e:
            logger.error(f"加载要素模板配置失败: {e}")
            return self._get_default_element_templates()
    
    async def _execute_query(self, query: str, params: List = None) -> List[Dict]:
        """执行数据库查询（模拟实现）"""
        # 实际实现中会连接数据库执行查询
        # 这里返回空列表，实际使用时需要实现真实的数据库查询
        logger.warning("使用模拟数据库查询，实际部署时需要实现真实的数据库连接")
        return []

    async def _load_extraction_rules(self) -> Dict[int, List[ExtractionRuleModel]]:
        """从数据库加载提取规则配置"""
        if not self.db:
            return self._get_default_extraction_rules()

        try:
            query = """
            SELECT id, element_template_id, rule_name, rule_type, rule_config,
                   priority, confidence_score, is_active, created_at, updated_at
            FROM element_extraction_rules
            WHERE is_active = TRUE
            ORDER BY element_template_id, priority DESC, confidence_score DESC
            """

            rows = await self._execute_query(query)
            rules_by_template = {}

            for row in rows:
                rule = ExtractionRuleModel(
                    id=row['id'],
                    element_template_id=row['element_template_id'],
                    rule_name=row['rule_name'],
                    rule_type=row['rule_type'],
                    rule_config=row['rule_config'],
                    priority=row['priority'],
                    confidence_score=row['confidence_score'],
                    is_active=row['is_active'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at']
                )

                template_id = rule.element_template_id
                if template_id not in rules_by_template:
                    rules_by_template[template_id] = []
                rules_by_template[template_id].append(rule)

            logger.info(f"加载了 {sum(len(rules) for rules in rules_by_template.values())} 个提取规则配置")
            return rules_by_template

        except Exception as e:
            logger.error(f"加载提取规则配置失败: {e}")
            return self._get_default_extraction_rules()

    async def _load_validation_rules(self) -> Dict[int, List[ValidationRuleModel]]:
        """从数据库加载验证规则配置"""
        if not self.db:
            return self._get_default_validation_rules()

        try:
            query = """
            SELECT id, element_template_id, validation_name, validation_type, validation_config,
                   error_message, warning_message, is_blocking, is_active, created_at, updated_at
            FROM element_validation_rules
            WHERE is_active = TRUE
            ORDER BY element_template_id, validation_name
            """

            rows = await self._execute_query(query)
            rules_by_template = {}

            for row in rows:
                rule = ValidationRuleModel(
                    id=row['id'],
                    element_template_id=row['element_template_id'],
                    validation_name=row['validation_name'],
                    validation_type=row['validation_type'],
                    validation_config=row['validation_config'],
                    error_message=row['error_message'],
                    warning_message=row['warning_message'],
                    is_blocking=row['is_blocking'],
                    is_active=row['is_active'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at']
                )

                template_id = rule.element_template_id
                if template_id not in rules_by_template:
                    rules_by_template[template_id] = []
                rules_by_template[template_id].append(rule)

            logger.info(f"加载了 {sum(len(rules) for rules in rules_by_template.values())} 个验证规则配置")
            return rules_by_template

        except Exception as e:
            logger.error(f"加载验证规则配置失败: {e}")
            return self._get_default_validation_rules()

    async def _load_element_relationships(self) -> Dict[str, List[ElementRelationshipModel]]:
        """从数据库加载要素关系配置"""
        if not self.db:
            return self._get_default_element_relationships()

        try:
            query = """
            SELECT er.id, er.relationship_name, er.relationship_type, er.source_elements,
                   er.target_elements, er.relationship_config, er.contract_type_id,
                   er.is_active, er.created_at, er.updated_at, ct.type_code
            FROM element_relationships er
            LEFT JOIN contract_types ct ON er.contract_type_id = ct.id
            WHERE er.is_active = TRUE
            ORDER BY er.relationship_name
            """

            rows = await self._execute_query(query)
            relationships_by_type = {}

            for row in rows:
                relationship = ElementRelationshipModel(
                    id=row['id'],
                    relationship_name=row['relationship_name'],
                    relationship_type=row['relationship_type'],
                    source_elements=row['source_elements'],
                    target_elements=row['target_elements'],
                    relationship_config=row['relationship_config'],
                    contract_type_id=row['contract_type_id'],
                    is_active=row['is_active'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at']
                )

                # 按合同类型分组
                contract_type = row['type_code'] or 'general'
                if contract_type not in relationships_by_type:
                    relationships_by_type[contract_type] = []
                relationships_by_type[contract_type].append(relationship)

            logger.info(f"加载了 {sum(len(rels) for rels in relationships_by_type.values())} 个要素关系配置")
            return relationships_by_type

        except Exception as e:
            logger.error(f"加载要素关系配置失败: {e}")
            return self._get_default_element_relationships()

    # 默认配置方法（用于无数据库连接时的降级）
    def _get_default_contract_types(self) -> Dict[str, ContractTypeModel]:
        """获取默认合同类型配置"""
        return {
            'general': ContractTypeModel(
                id=1, type_code='general', type_name='通用合同',
                type_description='适用于所有合同类型的通用配置',
                is_active=True, sort_order=0
            ),
            'sales': ContractTypeModel(
                id=2, type_code='sales', type_name='销售合同',
                type_description='商品销售相关的合同',
                is_active=True, sort_order=1
            ),
            'service': ContractTypeModel(
                id=3, type_code='service', type_name='服务合同',
                type_description='服务提供相关的合同',
                is_active=True, sort_order=2
            )
        }

    def _get_default_element_categories(self) -> Dict[str, ElementCategoryModel]:
        """获取默认要素分类配置"""
        return {
            'document_structure': ElementCategoryModel(
                id=1, category_code='document_structure', category_name='文档结构要素',
                category_description='合同文档的结构性要素',
                category_level=1, category_path='document_structure',
                is_active=True, sort_order=1
            ),
            'party_info': ElementCategoryModel(
                id=2, category_code='party_info', category_name='当事方信息',
                category_description='合同当事方的基本信息',
                category_level=1, category_path='party_info',
                is_active=True, sort_order=2
            )
        }

    def _get_default_element_templates(self) -> Dict[str, List[ElementTemplateModel]]:
        """获取默认要素模板配置"""
        return {
            'general': [
                ElementTemplateModel(
                    id=1, element_name='合同标题', element_code='contract_title',
                    element_display_name='合同标题', element_description='合同文档的标题',
                    element_category_id=1, data_type='text', is_required=True,
                    weight=1.0, sort_order=1, is_active=True, version=1
                )
            ]
        }

    def _get_default_extraction_rules(self) -> Dict[int, List[ExtractionRuleModel]]:
        """获取默认提取规则配置"""
        return {}

    def _get_default_validation_rules(self) -> Dict[int, List[ValidationRuleModel]]:
        """获取默认验证规则配置"""
        return {}

    def _get_default_element_relationships(self) -> Dict[str, List[ElementRelationshipModel]]:
        """获取默认要素关系配置"""
        return {}
