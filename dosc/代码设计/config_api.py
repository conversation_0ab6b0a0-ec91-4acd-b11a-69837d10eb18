"""
配置化要素管理系统 - API接口
提供要素配置的CRUD操作API
"""

from fastapi import APIRouter, HTTPException, Depends, Query
from typing import List, Optional, Dict, Any
import logging

from config_models import (
    ContractTypeModel, ElementCategoryModel, ElementTemplateModel,
    ExtractionRuleModel, ValidationRuleModel, ElementRelationshipModel,
    ElementTemplateCreateRequest, ElementTemplateUpdateRequest,
    ConfigurationExportModel, ConfigurationImportRequest
)
from config_manager import ConfigurationManager

logger = logging.getLogger(__name__)

# 创建API路由器
config_router = APIRouter(prefix="/api/v1/config", tags=["配置管理"])

# 依赖注入：获取配置管理器实例
async def get_config_manager() -> ConfigurationManager:
    """获取配置管理器实例"""
    # 实际实现中应该从应用上下文获取
    # 这里返回一个模拟实例
    return ConfigurationManager()

# =====================================================
# 合同类型管理API
# =====================================================

@config_router.get("/contract-types", response_model=List[ContractTypeModel])
async def get_contract_types(
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取所有合同类型"""
    try:
        contract_types = await config_manager.get_contract_types()
        return list(contract_types.values())
    except Exception as e:
        logger.error(f"获取合同类型失败: {e}")
        raise HTTPException(status_code=500, detail="获取合同类型失败")

@config_router.get("/contract-types/{type_code}", response_model=ContractTypeModel)
async def get_contract_type(
    type_code: str,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取指定合同类型"""
    try:
        contract_type = await config_manager.get_contract_type(type_code)
        if not contract_type:
            raise HTTPException(status_code=404, detail=f"合同类型 {type_code} 不存在")
        return contract_type
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取合同类型 {type_code} 失败: {e}")
        raise HTTPException(status_code=500, detail="获取合同类型失败")

# =====================================================
# 要素分类管理API
# =====================================================

@config_router.get("/element-categories", response_model=List[ElementCategoryModel])
async def get_element_categories(
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取所有要素分类"""
    try:
        categories = await config_manager.get_element_categories()
        return list(categories.values())
    except Exception as e:
        logger.error(f"获取要素分类失败: {e}")
        raise HTTPException(status_code=500, detail="获取要素分类失败")

@config_router.get("/element-categories/{category_code}/hierarchy", response_model=List[ElementCategoryModel])
async def get_category_hierarchy(
    category_code: str,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取分类层级结构"""
    try:
        hierarchy = await config_manager.get_category_hierarchy(category_code)
        return hierarchy
    except Exception as e:
        logger.error(f"获取分类层级结构失败: {e}")
        raise HTTPException(status_code=500, detail="获取分类层级结构失败")

# =====================================================
# 要素模板管理API
# =====================================================

@config_router.get("/element-templates", response_model=List[ElementTemplateModel])
async def get_element_templates(
    contract_type: Optional[str] = Query(None, description="合同类型"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    is_active: Optional[bool] = Query(None, description="是否启用"),
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取要素模板列表"""
    try:
        templates = await config_manager.get_element_templates(contract_type)
        
        # 应用过滤条件
        if category_id is not None:
            templates = [t for t in templates if t.element_category_id == category_id]
        
        if is_active is not None:
            templates = [t for t in templates if t.is_active == is_active]
        
        return templates
    except Exception as e:
        logger.error(f"获取要素模板失败: {e}")
        raise HTTPException(status_code=500, detail="获取要素模板失败")

@config_router.get("/element-templates/{element_code}", response_model=ElementTemplateModel)
async def get_element_template(
    element_code: str,
    contract_type: Optional[str] = Query(None, description="合同类型"),
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取指定要素模板"""
    try:
        template = await config_manager.get_element_template(element_code, contract_type)
        if not template:
            raise HTTPException(status_code=404, detail=f"要素模板 {element_code} 不存在")
        return template
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取要素模板 {element_code} 失败: {e}")
        raise HTTPException(status_code=500, detail="获取要素模板失败")

@config_router.post("/element-templates", response_model=ElementTemplateModel)
async def create_element_template(
    template_request: ElementTemplateCreateRequest,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """创建要素模板"""
    try:
        # 检查要素代码是否已存在
        existing_template = await config_manager.get_element_template(
            template_request.element_code, 
            template_request.contract_type_id
        )
        if existing_template:
            raise HTTPException(
                status_code=400, 
                detail=f"要素代码 {template_request.element_code} 已存在"
            )
        
        # 创建新模板（实际实现中需要数据库操作）
        new_template = ElementTemplateModel(
            **template_request.dict(),
            id=None,  # 由数据库生成
            is_active=True,
            version=1
        )
        
        # 这里应该调用数据库插入操作
        logger.info(f"创建要素模板: {template_request.element_name}")
        
        # 使配置缓存失效
        await config_manager.invalidate_cache()
        
        return new_template
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建要素模板失败: {e}")
        raise HTTPException(status_code=500, detail="创建要素模板失败")

@config_router.put("/element-templates/{template_id}", response_model=ElementTemplateModel)
async def update_element_template(
    template_id: int,
    template_request: ElementTemplateUpdateRequest,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """更新要素模板"""
    try:
        # 这里应该调用数据库更新操作
        logger.info(f"更新要素模板: {template_id}")
        
        # 使配置缓存失效
        await config_manager.invalidate_cache()
        
        # 返回更新后的模板（实际实现中从数据库获取）
        updated_template = ElementTemplateModel(
            id=template_id,
            element_name="更新后的模板",
            element_code="updated_template",
            element_category_id=1,
            data_type="text",
            is_active=True,
            version=1
        )
        
        return updated_template
        
    except Exception as e:
        logger.error(f"更新要素模板失败: {e}")
        raise HTTPException(status_code=500, detail="更新要素模板失败")

@config_router.delete("/element-templates/{template_id}")
async def delete_element_template(
    template_id: int,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """删除要素模板"""
    try:
        # 这里应该调用数据库删除操作（软删除）
        logger.info(f"删除要素模板: {template_id}")
        
        # 使配置缓存失效
        await config_manager.invalidate_cache()
        
        return {"message": "要素模板删除成功"}
        
    except Exception as e:
        logger.error(f"删除要素模板失败: {e}")
        raise HTTPException(status_code=500, detail="删除要素模板失败")

# =====================================================
# 提取规则管理API
# =====================================================

@config_router.get("/element-templates/{template_id}/extraction-rules", response_model=List[ExtractionRuleModel])
async def get_extraction_rules(
    template_id: int,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取要素模板的提取规则"""
    try:
        rules = await config_manager.get_extraction_rules(template_id)
        return rules
    except Exception as e:
        logger.error(f"获取提取规则失败: {e}")
        raise HTTPException(status_code=500, detail="获取提取规则失败")

@config_router.post("/element-templates/{template_id}/extraction-rules", response_model=ExtractionRuleModel)
async def create_extraction_rule(
    template_id: int,
    rule_data: Dict[str, Any],
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """创建提取规则"""
    try:
        # 这里应该调用数据库插入操作
        logger.info(f"为模板 {template_id} 创建提取规则")
        
        # 使配置缓存失效
        await config_manager.invalidate_cache()
        
        # 返回创建的规则（实际实现中从数据库获取）
        new_rule = ExtractionRuleModel(
            id=None,
            element_template_id=template_id,
            rule_name=rule_data.get("rule_name", "新规则"),
            rule_type=rule_data.get("rule_type", "regex"),
            rule_config=rule_data.get("rule_config", {}),
            priority=rule_data.get("priority", 0),
            confidence_score=rule_data.get("confidence_score", 0.8),
            is_active=True
        )
        
        return new_rule
        
    except Exception as e:
        logger.error(f"创建提取规则失败: {e}")
        raise HTTPException(status_code=500, detail="创建提取规则失败")

# =====================================================
# 验证规则管理API
# =====================================================

@config_router.get("/element-templates/{template_id}/validation-rules", response_model=List[ValidationRuleModel])
async def get_validation_rules(
    template_id: int,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取要素模板的验证规则"""
    try:
        rules = await config_manager.get_validation_rules(template_id)
        return rules
    except Exception as e:
        logger.error(f"获取验证规则失败: {e}")
        raise HTTPException(status_code=500, detail="获取验证规则失败")

# =====================================================
# 要素关系管理API
# =====================================================

@config_router.get("/element-relationships", response_model=List[ElementRelationshipModel])
async def get_element_relationships(
    contract_type: Optional[str] = Query(None, description="合同类型"),
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取要素关系配置"""
    try:
        relationships = await config_manager.get_element_relationships(contract_type)
        return relationships
    except Exception as e:
        logger.error(f"获取要素关系失败: {e}")
        raise HTTPException(status_code=500, detail="获取要素关系失败")

# =====================================================
# 配置导入导出API
# =====================================================

@config_router.get("/export", response_model=ConfigurationExportModel)
async def export_configuration(
    contract_type: Optional[str] = Query(None, description="合同类型"),
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """导出配置"""
    try:
        # 获取所有配置数据
        contract_types = await config_manager.get_contract_types()
        element_categories = await config_manager.get_element_categories()
        element_templates = await config_manager.get_element_templates(contract_type)
        element_relationships = await config_manager.get_element_relationships(contract_type)
        
        # 构建导出数据
        export_data = ConfigurationExportModel(
            contract_types=list(contract_types.values()),
            element_categories=list(element_categories.values()),
            element_templates=element_templates,
            extraction_rules=[],  # 需要额外查询
            validation_rules=[],  # 需要额外查询
            element_relationships=element_relationships
        )
        
        logger.info(f"导出配置完成，合同类型: {contract_type or '全部'}")
        return export_data
        
    except Exception as e:
        logger.error(f"导出配置失败: {e}")
        raise HTTPException(status_code=500, detail="导出配置失败")

@config_router.post("/import")
async def import_configuration(
    import_request: ConfigurationImportRequest,
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """导入配置"""
    try:
        # 验证导入数据
        if import_request.validate_before_import:
            # 执行数据验证
            pass
        
        # 创建备份
        if import_request.create_backup:
            # 创建当前配置的备份
            pass
        
        # 执行导入
        logger.info(f"开始导入配置，模式: {import_request.import_mode}")
        
        # 这里应该实现实际的导入逻辑
        # 根据import_mode执行不同的导入策略
        
        # 使配置缓存失效
        await config_manager.invalidate_cache()
        
        return {"message": "配置导入成功"}
        
    except Exception as e:
        logger.error(f"导入配置失败: {e}")
        raise HTTPException(status_code=500, detail="导入配置失败")

# =====================================================
# 配置管理工具API
# =====================================================

@config_router.post("/reload-cache")
async def reload_configuration_cache(
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """重新加载配置缓存"""
    try:
        await config_manager.reload_all_configurations()
        return {"message": "配置缓存重新加载成功"}
    except Exception as e:
        logger.error(f"重新加载配置缓存失败: {e}")
        raise HTTPException(status_code=500, detail="重新加载配置缓存失败")

@config_router.get("/cache-status")
async def get_cache_status(
    config_manager: ConfigurationManager = Depends(get_config_manager)
):
    """获取配置缓存状态"""
    try:
        cache_info = {
            "last_updated": config_manager.config_cache.last_updated,
            "cache_version": config_manager.config_cache.cache_version,
            "contract_types_count": len(config_manager.config_cache.contract_types or {}),
            "element_categories_count": len(config_manager.config_cache.element_categories or {}),
            "element_templates_count": sum(
                len(templates) for templates in (config_manager.config_cache.element_templates or {}).values()
            )
        }
        return cache_info
    except Exception as e:
        logger.error(f"获取缓存状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取缓存状态失败")
