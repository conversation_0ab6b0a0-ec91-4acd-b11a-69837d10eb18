-- =====================================================
-- AI合同审核系统 - 配置化要素管理系统数据库表结构
-- 第1-3周：配置化要素管理系统核心表设计
-- =====================================================

-- 1. 合同类型管理表
CREATE TABLE contract_types (
    id SERIAL PRIMARY KEY,
    type_code VARCHAR(50) UNIQUE NOT NULL,           -- 类型代码，如 'sales', 'service'
    type_name VARCHAR(100) NOT NULL,                 -- 类型名称，如 '销售合同'
    type_description TEXT,                           -- 类型描述
    parent_type_id INTEGER REFERENCES contract_types(id), -- 支持类型继承
    is_active BOOLEAN DEFAULT TRUE,                  -- 是否启用
    sort_order INTEGER DEFAULT 0,                   -- 排序
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 2. 合同类型识别配置表
CREATE TABLE contract_type_recognition (
    id SERIAL PRIMARY KEY,
    contract_type_id INTEGER NOT NULL REFERENCES contract_types(id),
    recognition_type VARCHAR(20) NOT NULL,           -- 'keyword', 'pattern', 'ai_prompt'
    recognition_config JSONB NOT NULL,               -- 识别配置
    weight DECIMAL(3,2) DEFAULT 1.0,                -- 权重
    confidence_threshold DECIMAL(3,2) DEFAULT 0.7,  -- 置信度阈值
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 要素分类表（支持多级分类）
CREATE TABLE element_categories (
    id SERIAL PRIMARY KEY,
    category_code VARCHAR(100) UNIQUE NOT NULL,      -- 分类代码
    category_name VARCHAR(200) NOT NULL,             -- 分类名称
    category_description TEXT,                       -- 分类描述
    parent_category_id INTEGER REFERENCES element_categories(id), -- 父分类
    category_level INTEGER DEFAULT 1,               -- 分类层级
    category_path VARCHAR(500),                      -- 分类路径，如 'root/document/title'
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

-- 4. 要素模板表（核心配置表）
CREATE TABLE contract_element_templates (
    id SERIAL PRIMARY KEY,
    element_name VARCHAR(200) NOT NULL,              -- 要素名称
    element_code VARCHAR(100) NOT NULL,              -- 要素代码
    element_display_name VARCHAR(200),               -- 显示名称
    element_description TEXT,                        -- 要素描述
    element_category_id INTEGER NOT NULL REFERENCES element_categories(id),
    contract_type_id INTEGER REFERENCES contract_types(id), -- NULL表示通用要素
    data_type VARCHAR(50) DEFAULT 'text',            -- 数据类型：text, amount, date, party_info, clause
    is_required BOOLEAN DEFAULT FALSE,               -- 是否必需
    is_unique BOOLEAN DEFAULT FALSE,                 -- 是否唯一
    weight DECIMAL(3,2) DEFAULT 1.0,                -- 权重
    sort_order INTEGER DEFAULT 0,                   -- 排序
    element_config JSONB,                           -- 要素配置（提取规则、验证规则等）
    validation_rules JSONB,                         -- 验证规则配置
    default_value TEXT,                             -- 默认值
    help_text TEXT,                                 -- 帮助文本
    is_active BOOLEAN DEFAULT TRUE,
    version INTEGER DEFAULT 1,                      -- 版本号
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    
    UNIQUE(element_code, contract_type_id, version)
);

-- 5. 要素提取规则表
CREATE TABLE element_extraction_rules (
    id SERIAL PRIMARY KEY,
    element_template_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    rule_name VARCHAR(200) NOT NULL,                -- 规则名称
    rule_type VARCHAR(50) NOT NULL,                 -- 规则类型：regex, keyword, ai_prompt, custom
    rule_config JSONB NOT NULL,                     -- 规则配置
    priority INTEGER DEFAULT 0,                     -- 优先级（数字越大优先级越高）
    confidence_score DECIMAL(3,2) DEFAULT 0.8,     -- 该规则的置信度
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. 要素验证规则表
CREATE TABLE element_validation_rules (
    id SERIAL PRIMARY KEY,
    element_template_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    validation_name VARCHAR(200) NOT NULL,          -- 验证规则名称
    validation_type VARCHAR(50) NOT NULL,           -- 验证类型：format, range, business_rule, custom
    validation_config JSONB NOT NULL,               -- 验证配置
    error_message TEXT,                             -- 错误消息模板
    warning_message TEXT,                           -- 警告消息模板
    is_blocking BOOLEAN DEFAULT FALSE,              -- 是否阻塞（验证失败是否阻止提取）
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. 要素关系表
CREATE TABLE element_relationships (
    id SERIAL PRIMARY KEY,
    relationship_name VARCHAR(200) NOT NULL,        -- 关系名称
    relationship_type VARCHAR(50) NOT NULL,         -- 关系类型：dependency, mutual_exclusion, composition, calculation
    source_elements JSONB NOT NULL,                 -- 源要素列表
    target_elements JSONB NOT NULL,                 -- 目标要素列表
    relationship_config JSONB,                      -- 关系配置
    contract_type_id INTEGER REFERENCES contract_types(id), -- 适用的合同类型
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 配置版本管理表
CREATE TABLE configuration_versions (
    id SERIAL PRIMARY KEY,
    version_name VARCHAR(100) NOT NULL,             -- 版本名称
    version_description TEXT,                       -- 版本描述
    contract_type_id INTEGER REFERENCES contract_types(id),
    version_number VARCHAR(20) NOT NULL,            -- 版本号
    is_current BOOLEAN DEFAULT FALSE,               -- 是否当前版本
    is_published BOOLEAN DEFAULT FALSE,             -- 是否已发布
    configuration_snapshot JSONB,                   -- 配置快照
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    published_at TIMESTAMP,
    published_by VARCHAR(100)
);

-- 9. 配置变更日志表
CREATE TABLE configuration_change_logs (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,               -- 变更的表名
    record_id INTEGER NOT NULL,                     -- 变更的记录ID
    change_type VARCHAR(20) NOT NULL,               -- 变更类型：INSERT, UPDATE, DELETE
    old_values JSONB,                               -- 变更前的值
    new_values JSONB,                               -- 变更后的值
    change_reason TEXT,                             -- 变更原因
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

-- 10. 要素提取结果表（运行时数据）
CREATE TABLE element_extraction_results (
    id SERIAL PRIMARY KEY,
    document_id VARCHAR(100) NOT NULL,              -- 文档ID
    element_template_id INTEGER NOT NULL REFERENCES contract_element_templates(id),
    extraction_rule_id INTEGER REFERENCES element_extraction_rules(id),
    extracted_value TEXT,                           -- 提取的值
    confidence_score DECIMAL(3,2),                  -- 置信度
    source_position JSONB,                          -- 在原文档中的位置
    verification_status VARCHAR(50),                -- 验证状态
    verification_details JSONB,                     -- 验证详情
    is_validated BOOLEAN DEFAULT FALSE,             -- 是否已验证
    validated_by VARCHAR(100),                      -- 验证人
    validated_at TIMESTAMP,                         -- 验证时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 索引创建
-- =====================================================

-- 合同类型表索引
CREATE INDEX idx_contract_types_code ON contract_types(type_code);
CREATE INDEX idx_contract_types_active ON contract_types(is_active);
CREATE INDEX idx_contract_types_parent ON contract_types(parent_type_id);

-- 合同类型识别配置索引
CREATE INDEX idx_contract_type_recognition_type ON contract_type_recognition(contract_type_id);
CREATE INDEX idx_contract_type_recognition_active ON contract_type_recognition(is_active);

-- 要素分类表索引
CREATE INDEX idx_element_categories_code ON element_categories(category_code);
CREATE INDEX idx_element_categories_parent ON element_categories(parent_category_id);
CREATE INDEX idx_element_categories_path ON element_categories(category_path);
CREATE INDEX idx_element_categories_active ON element_categories(is_active);

-- 要素模板表索引
CREATE INDEX idx_element_templates_code ON contract_element_templates(element_code);
CREATE INDEX idx_element_templates_category ON contract_element_templates(element_category_id);
CREATE INDEX idx_element_templates_contract_type ON contract_element_templates(contract_type_id);
CREATE INDEX idx_element_templates_active ON contract_element_templates(is_active);
CREATE INDEX idx_element_templates_version ON contract_element_templates(version);
CREATE INDEX idx_element_templates_data_type ON contract_element_templates(data_type);

-- 提取规则表索引
CREATE INDEX idx_extraction_rules_template ON element_extraction_rules(element_template_id);
CREATE INDEX idx_extraction_rules_type ON element_extraction_rules(rule_type);
CREATE INDEX idx_extraction_rules_active ON element_extraction_rules(is_active);
CREATE INDEX idx_extraction_rules_priority ON element_extraction_rules(priority DESC);

-- 验证规则表索引
CREATE INDEX idx_validation_rules_template ON element_validation_rules(element_template_id);
CREATE INDEX idx_validation_rules_type ON element_validation_rules(validation_type);
CREATE INDEX idx_validation_rules_active ON element_validation_rules(is_active);

-- 要素关系表索引
CREATE INDEX idx_element_relationships_type ON element_relationships(relationship_type);
CREATE INDEX idx_element_relationships_contract ON element_relationships(contract_type_id);
CREATE INDEX idx_element_relationships_active ON element_relationships(is_active);

-- 配置版本表索引
CREATE INDEX idx_config_versions_contract ON configuration_versions(contract_type_id);
CREATE INDEX idx_config_versions_current ON configuration_versions(is_current);
CREATE INDEX idx_config_versions_published ON configuration_versions(is_published);

-- 变更日志表索引
CREATE INDEX idx_change_logs_table_record ON configuration_change_logs(table_name, record_id);
CREATE INDEX idx_change_logs_created ON configuration_change_logs(created_at);
CREATE INDEX idx_change_logs_user ON configuration_change_logs(created_by);

-- 提取结果表索引
CREATE INDEX idx_extraction_results_document ON element_extraction_results(document_id);
CREATE INDEX idx_extraction_results_template ON element_extraction_results(element_template_id);
CREATE INDEX idx_extraction_results_rule ON element_extraction_results(extraction_rule_id);
CREATE INDEX idx_extraction_results_validated ON element_extraction_results(is_validated);
CREATE INDEX idx_extraction_results_created ON element_extraction_results(created_at);

-- =====================================================
-- 触发器和函数
-- =====================================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间戳触发器
CREATE TRIGGER update_contract_types_updated_at BEFORE UPDATE ON contract_types
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contract_type_recognition_updated_at BEFORE UPDATE ON contract_type_recognition
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_element_categories_updated_at BEFORE UPDATE ON element_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_element_templates_updated_at BEFORE UPDATE ON contract_element_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_extraction_rules_updated_at BEFORE UPDATE ON element_extraction_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_validation_rules_updated_at BEFORE UPDATE ON element_validation_rules
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_element_relationships_updated_at BEFORE UPDATE ON element_relationships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 分类路径自动更新触发器函数
CREATE OR REPLACE FUNCTION update_category_path()
RETURNS TRIGGER AS $$
DECLARE
    parent_path VARCHAR(500);
BEGIN
    IF NEW.parent_category_id IS NULL THEN
        NEW.category_path = NEW.category_code;
        NEW.category_level = 1;
    ELSE
        SELECT category_path, category_level INTO parent_path, NEW.category_level
        FROM element_categories WHERE id = NEW.parent_category_id;

        NEW.category_path = parent_path || '/' || NEW.category_code;
        NEW.category_level = NEW.category_level + 1;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_element_categories_path BEFORE INSERT OR UPDATE ON element_categories
    FOR EACH ROW EXECUTE FUNCTION update_category_path();

-- 配置变更日志触发器函数
CREATE OR REPLACE FUNCTION log_configuration_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO configuration_change_logs (table_name, record_id, change_type, old_values, created_by)
        VALUES (TG_TABLE_NAME, OLD.id, 'DELETE', row_to_json(OLD), current_user);
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO configuration_change_logs (table_name, record_id, change_type, old_values, new_values, created_by)
        VALUES (TG_TABLE_NAME, NEW.id, 'UPDATE', row_to_json(OLD), row_to_json(NEW), current_user);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO configuration_change_logs (table_name, record_id, change_type, new_values, created_by)
        VALUES (TG_TABLE_NAME, NEW.id, 'INSERT', row_to_json(NEW), current_user);
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- 为配置表创建变更日志触发器
CREATE TRIGGER log_contract_types_changes AFTER INSERT OR UPDATE OR DELETE ON contract_types
    FOR EACH ROW EXECUTE FUNCTION log_configuration_changes();

CREATE TRIGGER log_element_templates_changes AFTER INSERT OR UPDATE OR DELETE ON contract_element_templates
    FOR EACH ROW EXECUTE FUNCTION log_configuration_changes();

CREATE TRIGGER log_extraction_rules_changes AFTER INSERT OR UPDATE OR DELETE ON element_extraction_rules
    FOR EACH ROW EXECUTE FUNCTION log_configuration_changes();

CREATE TRIGGER log_validation_rules_changes AFTER INSERT OR UPDATE OR DELETE ON element_validation_rules
    FOR EACH ROW EXECUTE FUNCTION log_configuration_changes();
