"""
配置化要素管理系统 - 数据模型定义
基于数据库表结构的 Pydantic 模型定义
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Optional, Any, Union
from datetime import datetime
from enum import Enum
import json

class ContractTypeModel(BaseModel):
    """合同类型模型"""
    id: Optional[int] = None
    type_code: str = Field(..., min_length=1, max_length=50, description="类型代码")
    type_name: str = Field(..., min_length=1, max_length=100, description="类型名称")
    type_description: Optional[str] = Field(None, description="类型描述")
    parent_type_id: Optional[int] = Field(None, description="父类型ID")
    is_active: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

class ContractTypeRecognitionModel(BaseModel):
    """合同类型识别配置模型"""
    id: Optional[int] = None
    contract_type_id: int = Field(..., description="合同类型ID")
    recognition_type: str = Field(..., description="识别类型: keyword, pattern, ai_prompt")
    recognition_config: Dict[str, Any] = Field(..., description="识别配置")
    weight: float = Field(1.0, ge=0.0, le=10.0, description="权重")
    confidence_threshold: float = Field(0.7, ge=0.0, le=1.0, description="置信度阈值")
    is_active: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @validator('recognition_type')
    def validate_recognition_type(cls, v):
        allowed_types = ['keyword', 'pattern', 'ai_prompt']
        if v not in allowed_types:
            raise ValueError(f'recognition_type must be one of {allowed_types}')
        return v

class ElementCategoryModel(BaseModel):
    """要素分类模型"""
    id: Optional[int] = None
    category_code: str = Field(..., min_length=1, max_length=100, description="分类代码")
    category_name: str = Field(..., min_length=1, max_length=200, description="分类名称")
    category_description: Optional[str] = Field(None, description="分类描述")
    parent_category_id: Optional[int] = Field(None, description="父分类ID")
    category_level: int = Field(1, ge=1, description="分类层级")
    category_path: Optional[str] = Field(None, max_length=500, description="分类路径")
    is_active: bool = Field(True, description="是否启用")
    sort_order: int = Field(0, description="排序")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

class ElementTemplateModel(BaseModel):
    """要素模板模型"""
    id: Optional[int] = None
    element_name: str = Field(..., min_length=1, max_length=200, description="要素名称")
    element_code: str = Field(..., min_length=1, max_length=100, description="要素代码")
    element_display_name: Optional[str] = Field(None, max_length=200, description="显示名称")
    element_description: Optional[str] = Field(None, description="要素描述")
    element_category_id: int = Field(..., description="要素分类ID")
    contract_type_id: Optional[int] = Field(None, description="合同类型ID，NULL表示通用要素")
    data_type: str = Field('text', description="数据类型")
    is_required: bool = Field(False, description="是否必需")
    is_unique: bool = Field(False, description="是否唯一")
    weight: float = Field(1.0, ge=0.0, le=10.0, description="权重")
    sort_order: int = Field(0, description="排序")
    element_config: Optional[Dict[str, Any]] = Field(None, description="要素配置")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="验证规则配置")
    default_value: Optional[str] = Field(None, description="默认值")
    help_text: Optional[str] = Field(None, description="帮助文本")
    is_active: bool = Field(True, description="是否启用")
    version: int = Field(1, ge=1, description="版本号")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None

    @validator('data_type')
    def validate_data_type(cls, v):
        allowed_types = ['text', 'amount', 'date', 'party_info', 'clause', 'number', 'boolean']
        if v not in allowed_types:
            raise ValueError(f'data_type must be one of {allowed_types}')
        return v

class ExtractionRuleModel(BaseModel):
    """要素提取规则模型"""
    id: Optional[int] = None
    element_template_id: int = Field(..., description="要素模板ID")
    rule_name: str = Field(..., min_length=1, max_length=200, description="规则名称")
    rule_type: str = Field(..., description="规则类型")
    rule_config: Dict[str, Any] = Field(..., description="规则配置")
    priority: int = Field(0, description="优先级")
    confidence_score: float = Field(0.8, ge=0.0, le=1.0, description="置信度")
    is_active: bool = Field(True, description="是否启用")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @validator('rule_type')
    def validate_rule_type(cls, v):
        allowed_types = ['regex', 'keyword', 'ai_prompt', 'custom', 'semantic']
        if v not in allowed_types:
            raise ValueError(f'rule_type must be one of {allowed_types}')
        return v

class ValidationRuleModel(BaseModel):
    """要素验证规则模型"""
    id: Optional[int] = None
    element_template_id: int = Field(..., description="要素模板ID")
    validation_name: str = Field(..., min_length=1, max_length=200, description="验证规则名称")
    validation_type: str = Field(..., description="验证类型")
    validation_config: Dict[str, Any] = Field(..., description="验证配置")
    error_message: Optional[str] = Field(None, description="错误消息模板")
    warning_message: Optional[str] = Field(None, description="警告消息模板")
    is_blocking: bool = Field(False, description="是否阻塞")
    is_active: bool = Field(True, description="是否启用")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @validator('validation_type')
    def validate_validation_type(cls, v):
        allowed_types = ['format', 'range', 'business_rule', 'custom', 'regex']
        if v not in allowed_types:
            raise ValueError(f'validation_type must be one of {allowed_types}')
        return v

class ElementRelationshipModel(BaseModel):
    """要素关系模型"""
    id: Optional[int] = None
    relationship_name: str = Field(..., min_length=1, max_length=200, description="关系名称")
    relationship_type: str = Field(..., description="关系类型")
    source_elements: List[str] = Field(..., description="源要素列表")
    target_elements: List[str] = Field(..., description="目标要素列表")
    relationship_config: Optional[Dict[str, Any]] = Field(None, description="关系配置")
    contract_type_id: Optional[int] = Field(None, description="适用的合同类型")
    is_active: bool = Field(True, description="是否启用")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @validator('relationship_type')
    def validate_relationship_type(cls, v):
        allowed_types = ['dependency', 'mutual_exclusion', 'composition', 'calculation']
        if v not in allowed_types:
            raise ValueError(f'relationship_type must be one of {allowed_types}')
        return v

class ConfigurationVersionModel(BaseModel):
    """配置版本模型"""
    id: Optional[int] = None
    version_name: str = Field(..., min_length=1, max_length=100, description="版本名称")
    version_description: Optional[str] = Field(None, description="版本描述")
    contract_type_id: Optional[int] = Field(None, description="合同类型ID")
    version_number: str = Field(..., min_length=1, max_length=20, description="版本号")
    is_current: bool = Field(False, description="是否当前版本")
    is_published: bool = Field(False, description="是否已发布")
    configuration_snapshot: Optional[Dict[str, Any]] = Field(None, description="配置快照")
    created_at: Optional[datetime] = None
    created_by: Optional[str] = None
    published_at: Optional[datetime] = None
    published_by: Optional[str] = None

class ConfigurationChangeLogModel(BaseModel):
    """配置变更日志模型"""
    id: Optional[int] = None
    table_name: str = Field(..., min_length=1, max_length=100, description="变更的表名")
    record_id: int = Field(..., description="变更的记录ID")
    change_type: str = Field(..., description="变更类型")
    old_values: Optional[Dict[str, Any]] = Field(None, description="变更前的值")
    new_values: Optional[Dict[str, Any]] = Field(None, description="变更后的值")
    change_reason: Optional[str] = Field(None, description="变更原因")
    created_at: Optional[datetime] = None
    created_by: Optional[str] = None

    @validator('change_type')
    def validate_change_type(cls, v):
        allowed_types = ['INSERT', 'UPDATE', 'DELETE']
        if v not in allowed_types:
            raise ValueError(f'change_type must be one of {allowed_types}')
        return v

class ElementExtractionResultModel(BaseModel):
    """要素提取结果模型"""
    id: Optional[int] = None
    document_id: str = Field(..., min_length=1, max_length=100, description="文档ID")
    element_template_id: int = Field(..., description="要素模板ID")
    extraction_rule_id: Optional[int] = Field(None, description="提取规则ID")
    extracted_value: Optional[str] = Field(None, description="提取的值")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="置信度")
    source_position: Optional[Dict[str, Any]] = Field(None, description="在原文档中的位置")
    verification_status: Optional[str] = Field(None, description="验证状态")
    verification_details: Optional[Dict[str, Any]] = Field(None, description="验证详情")
    is_validated: bool = Field(False, description="是否已验证")
    validated_by: Optional[str] = Field(None, description="验证人")
    validated_at: Optional[datetime] = None
    created_at: Optional[datetime] = None

# 请求和响应模型
class ElementTemplateCreateRequest(BaseModel):
    """要素模板创建请求"""
    element_name: str = Field(..., min_length=1, max_length=200)
    element_code: str = Field(..., min_length=1, max_length=100)
    element_display_name: Optional[str] = Field(None, max_length=200)
    element_description: Optional[str] = None
    element_category_id: int = Field(...)
    contract_type_id: Optional[int] = None
    data_type: str = Field('text')
    is_required: bool = Field(False)
    is_unique: bool = Field(False)
    weight: float = Field(1.0, ge=0.0, le=10.0)
    sort_order: int = Field(0)
    element_config: Optional[Dict[str, Any]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    default_value: Optional[str] = None
    help_text: Optional[str] = None

class ElementTemplateUpdateRequest(BaseModel):
    """要素模板更新请求"""
    element_name: Optional[str] = Field(None, min_length=1, max_length=200)
    element_display_name: Optional[str] = Field(None, max_length=200)
    element_description: Optional[str] = None
    is_required: Optional[bool] = None
    is_unique: Optional[bool] = None
    weight: Optional[float] = Field(None, ge=0.0, le=10.0)
    sort_order: Optional[int] = None
    element_config: Optional[Dict[str, Any]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    default_value: Optional[str] = None
    help_text: Optional[str] = None
    is_active: Optional[bool] = None

class ConfigurationExportModel(BaseModel):
    """配置导出模型"""
    contract_types: List[ContractTypeModel] = []
    element_categories: List[ElementCategoryModel] = []
    element_templates: List[ElementTemplateModel] = []
    extraction_rules: List[ExtractionRuleModel] = []
    validation_rules: List[ValidationRuleModel] = []
    element_relationships: List[ElementRelationshipModel] = []
    export_timestamp: datetime = Field(default_factory=datetime.now)
    export_version: str = Field("1.0.0")

class ConfigurationImportRequest(BaseModel):
    """配置导入请求"""
    configuration_data: ConfigurationExportModel = Field(...)
    import_mode: str = Field('merge', description="导入模式: merge, replace, append")
    validate_before_import: bool = Field(True, description="导入前是否验证")
    create_backup: bool = Field(True, description="是否创建备份")

    @validator('import_mode')
    def validate_import_mode(cls, v):
        allowed_modes = ['merge', 'replace', 'append']
        if v not in allowed_modes:
            raise ValueError(f'import_mode must be one of {allowed_modes}')
        return v
