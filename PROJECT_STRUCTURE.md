# AI合同审核系统 - 项目结构说明

## 📁 项目目录结构

```
newProject/
├── .gitignore                    # Git忽略文件配置
├── PROJECT_STRUCTURE.md          # 项目结构说明（本文件）
├── 技术验证报告.md                # 技术验证总结报告
├── dosc/                         # 项目文档目录
│   ├── 需求文档.md               # 详细需求规格说明
│   ├── 设计文档.md               # 系统设计文档
│   └── 计划文档.md               # 项目计划文档
└── mvp/                          # MVP实现目录
    ├── README.md                 # MVP使用说明
    ├── requirements.txt          # Python依赖包列表
    ├── .env.example             # 环境变量配置示例
    ├── config.py                # 基础配置文件
    ├── config_manager.py        # 配置管理器
    ├── main.py                  # FastAPI主应用
    ├── start_mvp.py             # 启动脚本
    ├── setup_qwen.py            # 千问API配置向导
    ├── models.py                # 数据模型定义
    ├── document_processor.py    # 文档处理器
    ├── contract_analyzer.py     # 合同分析器
    ├── qwen_api_client.py       # 千问API客户端
    ├── test_mvp.py              # 测试套件
    ├── static/                  # 静态文件目录
    │   └── index.html           # 简单的Web界面
    ├── logs/                    # 日志文件目录
    ├── temp/                    # 临时文件目录
    └── test_files/              # 测试文件目录
```

## 🔧 核心模块说明

### 1. 主应用模块
- **main.py**: FastAPI应用主入口，定义API路由和中间件
- **start_mvp.py**: 系统启动脚本，包含健康检查和依赖验证
- **config.py**: 基础配置常量
- **config_manager.py**: 动态配置管理，支持环境变量

### 2. 业务逻辑模块
- **document_processor.py**: Word文档解析和要素提取
- **contract_analyzer.py**: 合同智能分析和风险识别
- **qwen_api_client.py**: 千问3 API客户端封装
- **models.py**: 数据模型和响应结构定义

### 3. 配置和工具
- **setup_qwen.py**: 交互式API配置向导
- **.env.example**: 环境变量配置模板
- **test_mvp.py**: 完整的测试套件

## 🚀 快速开始

### 1. 环境准备
```bash
cd mvp
pip install -r requirements.txt
```

### 2. 配置API
```bash
python setup_qwen.py
```

### 3. 启动服务
```bash
python start_mvp.py
```

### 4. 运行测试
```bash
python -m pytest test_mvp.py -v
```

## 📋 功能特性

### ✅ 已实现功能
- Word文档解析和结构化处理
- 千问3 API集成和智能分析
- 基础要素提取（当事方、金额、日期等）
- 合同类型自动识别
- 异步文件处理和API调用
- 完整的测试套件
- 健康检查和错误处理

### 🔄 技术特点
- **异步处理**: 基于FastAPI的异步架构
- **AI集成**: 真实的千问3大语言模型API
- **降级机制**: AI服务不可用时自动降级到规则匹配
- **配置化**: 支持环境变量和动态配置
- **测试完备**: 单元测试、集成测试、性能测试

## 📝 开发说明

### 代码规范
- 使用Python 3.8+
- 遵循PEP 8代码风格
- 中文注释和文档
- 异步优先的设计模式

### 测试策略
- 单元测试覆盖核心组件
- 集成测试验证API接口
- 性能测试确保处理效率
- 真实API测试（需要有效密钥）

### 部署要求
- Python 3.8+
- 有效的千问3 API密钥
- 50MB以下的Word文档支持
- 网络连接（用于API调用）

## 🔗 相关文档

- [MVP使用说明](mvp/README.md)
- [需求文档](dosc/需求文档.md)
- [设计文档](dosc/设计文档.md)
- [技术验证报告](技术验证报告.md)
