-- =====================================================
-- 配置化要素管理系统 - 示例数据
-- =====================================================

-- 1. 插入合同类型数据
INSERT INTO contract_types (type_code, type_name, type_description, sort_order) VALUES
('general', '通用合同', '适用于所有合同类型的通用配置', 0),
('sales', '销售合同', '商品销售相关的合同', 1),
('service', '服务合同', '服务提供相关的合同', 2),
('lease', '租赁合同', '房屋、设备等租赁合同', 3),
('employment', '劳动合同', '劳动关系相关合同', 4),
('procurement', '采购合同', '商品采购相关合同', 5);

-- 2. 插入合同类型识别配置
INSERT INTO contract_type_recognition (contract_type_id, recognition_type, recognition_config, weight) VALUES
-- 销售合同识别规则
((SELECT id FROM contract_types WHERE type_code = 'sales'), 'keyword', 
 '{"keywords": [{"keyword": "销售合同", "weight": 1.0}, {"keyword": "买卖合同", "weight": 0.9}, {"keyword": "商品销售", "weight": 0.8}]}', 1.0),
((SELECT id FROM contract_types WHERE type_code = 'sales'), 'pattern', 
 '{"patterns": [{"pattern": ".*销售.*合同.*", "weight": 0.8}, {"pattern": ".*买卖.*协议.*", "weight": 0.7}]}', 0.8),

-- 服务合同识别规则
((SELECT id FROM contract_types WHERE type_code = 'service'), 'keyword', 
 '{"keywords": [{"keyword": "服务合同", "weight": 1.0}, {"keyword": "服务协议", "weight": 0.9}, {"keyword": "技术服务", "weight": 0.8}]}', 1.0),
((SELECT id FROM contract_types WHERE type_code = 'service'), 'pattern', 
 '{"patterns": [{"pattern": ".*服务.*合同.*", "weight": 0.8}, {"pattern": ".*技术.*服务.*", "weight": 0.7}]}', 0.8),

-- 租赁合同识别规则
((SELECT id FROM contract_types WHERE type_code = 'lease'), 'keyword', 
 '{"keywords": [{"keyword": "租赁合同", "weight": 1.0}, {"keyword": "租房合同", "weight": 0.9}, {"keyword": "房屋租赁", "weight": 0.8}]}', 1.0);

-- 3. 插入要素分类数据
INSERT INTO element_categories (category_code, category_name, category_description, parent_category_id, sort_order) VALUES
-- 一级分类
('document_structure', '文档结构要素', '合同文档的结构性要素', NULL, 1),
('party_info', '当事方信息', '合同当事方的基本信息', NULL, 2),
('general_clauses', '通用条款要素', '适用于所有合同类型的通用条款', NULL, 3),
('financial_terms', '财务条款', '与金钱、费用相关的条款', NULL, 4),
('time_terms', '时间条款', '与时间、期限相关的条款', NULL, 5),
('special_clauses', '专用条款要素', '特定合同类型的专用条款', NULL, 6),

-- 文档结构要素子分类
('doc_title', '合同标题', '合同的标题信息', (SELECT id FROM element_categories WHERE category_code = 'document_structure'), 1),
('doc_number', '合同编号', '合同的编号信息', (SELECT id FROM element_categories WHERE category_code = 'document_structure'), 2),
('doc_metadata', '文档元数据', '文档的元数据信息', (SELECT id FROM element_categories WHERE category_code = 'document_structure'), 3),

-- 当事方信息子分类
('party_basic', '基本信息', '当事方的基本信息', (SELECT id FROM element_categories WHERE category_code = 'party_info'), 1),
('party_contact', '联系信息', '当事方的联系方式', (SELECT id FROM element_categories WHERE category_code = 'party_info'), 2),
('party_legal', '法律信息', '当事方的法律相关信息', (SELECT id FROM element_categories WHERE category_code = 'party_info'), 3),

-- 专用条款子分类
('sales_clauses', '销售合同专用条款', '销售合同特有的条款', (SELECT id FROM element_categories WHERE category_code = 'special_clauses'), 1),
('service_clauses', '服务合同专用条款', '服务合同特有的条款', (SELECT id FROM element_categories WHERE category_code = 'special_clauses'), 2),
('lease_clauses', '租赁合同专用条款', '租赁合同特有的条款', (SELECT id FROM element_categories WHERE category_code = 'special_clauses'), 3);

-- 4. 插入要素模板数据
INSERT INTO contract_element_templates (
    element_name, element_code, element_display_name, element_description, 
    element_category_id, contract_type_id, data_type, is_required, weight, sort_order,
    element_config, validation_rules
) VALUES

-- 通用文档结构要素
('合同标题', 'contract_title', '合同标题', '合同文档的标题', 
 (SELECT id FROM element_categories WHERE category_code = 'doc_title'), NULL, 'text', TRUE, 1.0, 1,
 '{"extraction_strategy": "multi_rule", "position_preference": "top"}',
 '{"min_length": 5, "max_length": 200, "required_keywords": ["合同", "协议", "契约"]}'),

('合同编号', 'contract_number', '合同编号', '合同的唯一编号', 
 (SELECT id FROM element_categories WHERE category_code = 'doc_number'), NULL, 'text', FALSE, 0.8, 2,
 '{"extraction_strategy": "pattern_match"}',
 '{"pattern": "^[A-Z0-9\\-_]{5,50}$"}'),

-- 通用当事方信息
('甲方名称', 'party_a_name', '甲方名称', '合同甲方的名称', 
 (SELECT id FROM element_categories WHERE category_code = 'party_basic'), NULL, 'party_info', TRUE, 1.0, 10,
 '{"extraction_strategy": "pattern_match", "party_role": "甲方"}',
 '{"min_length": 2, "max_length": 200}'),

('乙方名称', 'party_b_name', '乙方名称', '合同乙方的名称', 
 (SELECT id FROM element_categories WHERE category_code = 'party_basic'), NULL, 'party_info', TRUE, 1.0, 11,
 '{"extraction_strategy": "pattern_match", "party_role": "乙方"}',
 '{"min_length": 2, "max_length": 200}'),

-- 通用条款要素
('争议解决条款', 'dispute_resolution', '争议解决', '争议解决方式的条款', 
 (SELECT id FROM element_categories WHERE category_code = 'general_clauses'), NULL, 'clause', FALSE, 0.7, 20,
 '{"extraction_strategy": "semantic_match", "clause_type": "dispute_resolution"}',
 '{"required_keywords": ["争议", "纠纷", "仲裁", "诉讼"]}'),

('违约责任条款', 'breach_liability', '违约责任', '违约责任相关的条款', 
 (SELECT id FROM element_categories WHERE category_code = 'general_clauses'), NULL, 'clause', FALSE, 0.8, 21,
 '{"extraction_strategy": "semantic_match", "clause_type": "breach_liability"}',
 '{"required_keywords": ["违约", "责任", "赔偿"]}'),

-- 销售合同专用要素
('商品名称', 'product_name', '商品名称', '销售商品的名称', 
 (SELECT id FROM element_categories WHERE category_code = 'sales_clauses'), 
 (SELECT id FROM contract_types WHERE type_code = 'sales'), 'text', TRUE, 1.0, 30,
 '{"extraction_strategy": "pattern_match"}',
 '{"min_length": 2, "max_length": 500}'),

('商品价格', 'product_price', '商品价格', '销售商品的价格', 
 (SELECT id FROM element_categories WHERE category_code = 'sales_clauses'), 
 (SELECT id FROM contract_types WHERE type_code = 'sales'), 'amount', TRUE, 1.0, 31,
 '{"extraction_strategy": "amount_extraction", "currency_support": ["人民币", "美元"]}',
 '{"min_value": 0, "currency_required": true}'),

-- 服务合同专用要素
('服务内容', 'service_content', '服务内容', '提供服务的具体内容', 
 (SELECT id FROM element_categories WHERE category_code = 'service_clauses'), 
 (SELECT id FROM contract_types WHERE type_code = 'service'), 'text', TRUE, 1.0, 40,
 '{"extraction_strategy": "semantic_match"}',
 '{"min_length": 10, "max_length": 2000}'),

('服务费用', 'service_fee', '服务费用', '服务的费用', 
 (SELECT id FROM element_categories WHERE category_code = 'service_clauses'), 
 (SELECT id FROM contract_types WHERE type_code = 'service'), 'amount', TRUE, 1.0, 41,
 '{"extraction_strategy": "amount_extraction"}',
 '{"min_value": 0, "currency_required": true}'),

-- 租赁合同专用要素
('租赁物', 'lease_object', '租赁物', '租赁的标的物', 
 (SELECT id FROM element_categories WHERE category_code = 'lease_clauses'), 
 (SELECT id FROM contract_types WHERE type_code = 'lease'), 'text', TRUE, 1.0, 50,
 '{"extraction_strategy": "pattern_match"}',
 '{"min_length": 5, "max_length": 500}'),

('租金', 'rent_amount', '租金', '租赁的费用', 
 (SELECT id FROM element_categories WHERE category_code = 'lease_clauses'), 
 (SELECT id FROM contract_types WHERE type_code = 'lease'), 'amount', TRUE, 1.0, 51,
 '{"extraction_strategy": "amount_extraction"}',
 '{"min_value": 0, "currency_required": true}'),

('租赁期限', 'lease_term', '租赁期限', '租赁的时间期限', 
 (SELECT id FROM element_categories WHERE category_code = 'lease_clauses'), 
 (SELECT id FROM contract_types WHERE type_code = 'lease'), 'date', TRUE, 0.9, 52,
 '{"extraction_strategy": "date_range_extraction"}',
 '{"date_format": ["YYYY-MM-DD", "YYYY年MM月DD日"], "require_start_end": true}');

-- 5. 插入要素提取规则数据
INSERT INTO element_extraction_rules (element_template_id, rule_name, rule_type, rule_config, priority, confidence_score) VALUES

-- 合同标题提取规则
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'),
 '标题关键词匹配', 'regex',
 '{"pattern": "^(.{5,100}?)(合同|协议|契约|协定)", "group": 1, "flags": ["MULTILINE"]}',
 10, 0.9),

((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'),
 '首行标题匹配', 'regex',
 '{"pattern": "^(.{5,100})$", "group": 1, "position_limit": 3}',
 5, 0.6),

-- 合同编号提取规则
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_number'),
 '编号模式匹配', 'regex',
 '{"pattern": "合同编号[：:]?\\s*([A-Z0-9\\-_]{5,50})", "group": 1}',
 10, 0.8),

((SELECT id FROM contract_element_templates WHERE element_code = 'contract_number'),
 '编号关键词匹配', 'keyword',
 '{"keywords": ["合同编号", "协议编号", "编号"], "extract_after": true, "max_distance": 20}',
 5, 0.7),

-- 甲方名称提取规则
((SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'),
 '甲方标准格式', 'regex',
 '{"pattern": "甲\\s*方[：:]?\\s*([^，,；;\\n]{2,100})", "group": 1}',
 10, 0.9),

((SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'),
 '甲方详细信息', 'regex',
 '{"pattern": "甲\\s*方[：:]?\\s*([^（(]+)(?:\\([^)]*\\))?", "group": 1}',
 8, 0.8),

-- 乙方名称提取规则
((SELECT id FROM contract_element_templates WHERE element_code = 'party_b_name'),
 '乙方标准格式', 'regex',
 '{"pattern": "乙\\s*方[：:]?\\s*([^，,；;\\n]{2,100})", "group": 1}',
 10, 0.9),

-- 商品名称提取规则（销售合同）
((SELECT id FROM contract_element_templates WHERE element_code = 'product_name'),
 '商品名称模式', 'regex',
 '{"pattern": "(商品|货物|产品)名称[：:]?\\s*([^，,；;\\n]{2,200})", "group": 2}',
 10, 0.8),

((SELECT id FROM contract_element_templates WHERE element_code = 'product_name'),
 '标的物匹配', 'regex',
 '{"pattern": "标的[：:]?\\s*([^，,；;\\n]{2,200})", "group": 1}',
 8, 0.7),

-- 商品价格提取规则（销售合同）
((SELECT id FROM contract_element_templates WHERE element_code = 'product_price'),
 '价格金额匹配', 'regex',
 '{"pattern": "(价格|价款|金额)[：:]?\\s*(人民币)?\\s*([\\d,，]+(?:\\.\\d+)?)\\s*(元|万元)", "groups": [3, 4], "currency_group": 2}',
 10, 0.9),

-- 服务内容提取规则（服务合同）
((SELECT id FROM contract_element_templates WHERE element_code = 'service_content'),
 '服务内容条款', 'regex',
 '{"pattern": "服务内容[：:]?\\s*([^。]{10,500})", "group": 1}',
 10, 0.8),

-- 租金提取规则（租赁合同）
((SELECT id FROM contract_element_templates WHERE element_code = 'rent_amount'),
 '租金金额匹配', 'regex',
 '{"pattern": "租金[：:]?\\s*(人民币)?\\s*([\\d,，]+(?:\\.\\d+)?)\\s*(元|万元)(?:/月|/年)?", "groups": [2, 3], "currency_group": 1}',
 10, 0.9);

-- 6. 插入要素验证规则数据
INSERT INTO element_validation_rules (element_template_id, validation_name, validation_type, validation_config, error_message, is_blocking) VALUES

-- 合同标题验证规则
((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'),
 '标题长度验证', 'format',
 '{"min_length": 5, "max_length": 200}',
 '合同标题长度必须在5-200字符之间', TRUE),

((SELECT id FROM contract_element_templates WHERE element_code = 'contract_title'),
 '标题关键词验证', 'business_rule',
 '{"required_keywords": ["合同", "协议", "契约", "协定"], "match_any": true}',
 '合同标题必须包含"合同"、"协议"、"契约"或"协定"等关键词', FALSE),

-- 当事方名称验证规则
((SELECT id FROM contract_element_templates WHERE element_code = 'party_a_name'),
 '甲方名称长度验证', 'format',
 '{"min_length": 2, "max_length": 200}',
 '甲方名称长度必须在2-200字符之间', TRUE),

((SELECT id FROM contract_element_templates WHERE element_code = 'party_b_name'),
 '乙方名称长度验证', 'format',
 '{"min_length": 2, "max_length": 200}',
 '乙方名称长度必须在2-200字符之间', TRUE),

-- 金额验证规则
((SELECT id FROM contract_element_templates WHERE element_code = 'product_price'),
 '价格范围验证', 'range',
 '{"min_value": 0, "max_value": 999999999}',
 '商品价格必须大于0', TRUE),

((SELECT id FROM contract_element_templates WHERE element_code = 'service_fee'),
 '服务费用范围验证', 'range',
 '{"min_value": 0, "max_value": 999999999}',
 '服务费用必须大于0', TRUE),

((SELECT id FROM contract_element_templates WHERE element_code = 'rent_amount'),
 '租金范围验证', 'range',
 '{"min_value": 0, "max_value": 999999999}',
 '租金必须大于0', TRUE);

-- 7. 插入要素关系数据
INSERT INTO element_relationships (relationship_name, relationship_type, source_elements, target_elements, relationship_config, contract_type_id) VALUES

-- 依赖关系：商品价格依赖于商品名称
('商品价格依赖商品名称', 'dependency',
 '["product_price"]', '["product_name"]',
 '{"rule": "if_target_missing_remove_source", "description": "如果没有商品名称，则不应该有商品价格"}',
 (SELECT id FROM contract_types WHERE type_code = 'sales')),

-- 组合关系：当事方完整信息
('当事方完整信息组合', 'composition',
 '["party_a_name", "party_b_name"]', '["complete_party_info"]',
 '{"rule": "combine_all", "format": "甲方：{party_a_name}，乙方：{party_b_name}"}',
 NULL),

-- 互斥关系：不同的价格表述方式
('价格表述互斥', 'mutual_exclusion',
 '["product_price", "total_amount"]', '[]',
 '{"rule": "keep_highest_confidence", "description": "商品价格和总金额不应同时存在"}',
 (SELECT id FROM contract_types WHERE type_code = 'sales'));

-- 8. 插入配置版本数据
INSERT INTO configuration_versions (version_name, version_description, contract_type_id, version_number, is_current, is_published) VALUES
('通用配置 v1.0', '通用合同要素配置初始版本', NULL, '1.0.0', TRUE, TRUE),
('销售合同配置 v1.0', '销售合同专用配置初始版本', (SELECT id FROM contract_types WHERE type_code = 'sales'), '1.0.0', TRUE, TRUE),
('服务合同配置 v1.0', '服务合同专用配置初始版本', (SELECT id FROM contract_types WHERE type_code = 'service'), '1.0.0', TRUE, TRUE),
('租赁合同配置 v1.0', '租赁合同专用配置初始版本', (SELECT id FROM contract_types WHERE type_code = 'lease'), '1.0.0', TRUE, TRUE);
