"""
文档处理器 - 核心技术验证
基于python-docx的Word文档解析和文本提取功能
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import re
import jieba

from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

from models import DocumentInfo, ExtractedElement, ElementCategory

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """文档处理器 - 负责Word文档的解析和结构化处理"""
    
    def __init__(self):
        self.supported_formats = ['.doc', '.docx']
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        
    async def health_check(self) -> Dict[str, str]:
        """健康检查"""
        try:
            # 测试python-docx基本功能
            test_doc = Document()
            test_doc.add_paragraph("健康检查测试")
            return {"status": "healthy", "component": "document_processor"}
        except Exception as e:
            logger.error(f"文档处理器健康检查失败: {e}")
            return {"status": "unhealthy", "error": str(e)}
    
    async def process_document(self, file_path: str) -> DocumentInfo:
        """
        处理Word文档，提取基础信息和结构化内容
        
        技术验证重点：
        1. python-docx的文档解析能力
        2. 异步处理大文件的性能
        3. 文本提取和清理的准确性
        4. 结构化信息的获取
        """
        logger.info(f"开始处理文档: {file_path}")
        
        try:
            # 在线程池中执行文档解析（避免阻塞事件循环）
            loop = asyncio.get_event_loop()
            document_data = await loop.run_in_executor(
                None, self._parse_document_sync, file_path
            )
            
            # 构建DocumentInfo对象
            document_info = DocumentInfo(
                filename=Path(file_path).name,
                file_size=Path(file_path).stat().st_size,
                total_paragraphs=document_data['paragraph_count'],
                total_tables=document_data['table_count'],
                total_words=document_data['word_count'],
                created_time=document_data.get('created_time'),
                modified_time=document_data.get('modified_time'),
                author=document_data.get('author')
            )
            
            logger.info(f"文档处理完成: {document_info.total_paragraphs}段落, {document_info.total_words}字")
            return document_info
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            raise Exception(f"文档处理失败: {str(e)}")
    
    def _parse_document_sync(self, file_path: str) -> Dict:
        """同步解析文档（在线程池中执行）"""
        try:
            # 打开Word文档
            doc = Document(file_path)
            
            # 提取文档元数据
            core_props = doc.core_properties
            
            # 解析段落内容
            paragraphs_data = []
            total_words = 0
            
            for i, paragraph in enumerate(doc.paragraphs):
                if paragraph.text.strip():  # 跳过空段落
                    # 文本清理和预处理
                    cleaned_text = self._clean_text(paragraph.text)
                    # 精确字数统计：按Word标准计算
                    word_count = self._count_words_accurately(paragraph.text)
                    
                    paragraph_info = {
                        'index': i,
                        'text': paragraph.text,
                        'cleaned_text': cleaned_text,
                        'word_count': word_count,
                        'style': paragraph.style.name if paragraph.style else None,
                        'alignment': paragraph.alignment
                    }
                    paragraphs_data.append(paragraph_info)
                    total_words += word_count
            
            # 解析表格内容
            tables_data = []
            table_words = 0
            for i, table in enumerate(doc.tables):
                table_data = self._extract_table_data(table)
                # 统计表格中的字数
                for row in table_data:
                    for cell in row:
                        table_words += self._count_words_accurately(cell)

                table_info = {
                    'index': i,
                    'rows': len(table.rows),
                    'cols': len(table.columns) if table.rows else 0,
                    'data': table_data
                }
                tables_data.append(table_info)

            # 将表格字数加入总字数
            total_words += table_words
            
            return {
                'paragraph_count': len(paragraphs_data),
                'table_count': len(tables_data),
                'word_count': total_words,
                'paragraphs': paragraphs_data,
                'tables': tables_data,
                'created_time': core_props.created,
                'modified_time': core_props.modified,
                'author': core_props.author,
                'title': core_props.title
            }
            
        except Exception as e:
            logger.error(f"同步文档解析失败: {e}")
            raise
    
    def _clean_text(self, text: str) -> str:
        """文本清理和规范化"""
        if not text:
            return ""

        # 统一编码
        text = text.encode('utf-8').decode('utf-8')

        # 清理特殊字符，保留中文、英文、数字和常用标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】""''、。，；：！？]', '', text)

        # 规范化空白字符
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def _count_words_accurately(self, text: str) -> int:
        """精确的字数统计，尽量接近Word标准"""
        if not text:
            return 0

        # 方法1：简单字符计数（不包括空格）
        # 这个方法最接近Word的"字符数(不计空格)"
        chars_no_space = len(re.sub(r'\s', '', text))

        # 方法2：Word标准计数
        # 中文字符（包括中文标点）
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))

        # 英文单词（按空格分割）
        english_text = re.sub(r'[\u4e00-\u9fff]', ' ', text)  # 移除中文
        english_words = len([word for word in english_text.split() if re.search(r'[a-zA-Z0-9]', word)])

        # 标点符号和其他字符
        punctuation = len(re.findall(r'[^\u4e00-\u9fff\sa-zA-Z0-9]', text))

        # 使用更接近Word标准的计算方式
        # 优先使用字符数(不计空格)，这通常最准确
        return chars_no_space
    
    def _extract_table_data(self, table) -> List[List[str]]:
        """提取表格数据"""
        table_data = []
        try:
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = self._clean_text(cell.text)
                    row_data.append(cell_text)
                table_data.append(row_data)
        except Exception as e:
            logger.warning(f"表格数据提取失败: {e}")
        
        return table_data
    
    async def extract_basic_elements(self, file_path: str) -> List[ExtractedElement]:
        """
        提取基础文档要素
        
        技术验证重点：
        1. 正则表达式模式匹配的准确性
        2. 要素分类和置信度计算
        3. 位置信息的精确记录
        """
        logger.info("开始提取基础文档要素")
        
        try:
            # 在线程池中执行要素提取
            loop = asyncio.get_event_loop()
            elements = await loop.run_in_executor(
                None, self._extract_elements_sync, file_path
            )
            
            logger.info(f"基础要素提取完成: {len(elements)}个要素")
            return elements
            
        except Exception as e:
            logger.error(f"要素提取失败: {e}")
            raise
    
    def _extract_elements_sync(self, file_path: str) -> List[ExtractedElement]:
        """同步提取要素（在线程池中执行）"""
        elements = []
        
        try:
            doc = Document(file_path)
            full_text = '\n'.join([p.text for p in doc.paragraphs if p.text.strip()])
            
            # 提取合同标题
            title_element = self._extract_title(doc.paragraphs)
            if title_element:
                elements.append(title_element)
            
            # 提取当事方信息
            party_elements = self._extract_parties(full_text)
            elements.extend(party_elements)
            
            # 提取金额信息
            amount_elements = self._extract_amounts(full_text)
            elements.extend(amount_elements)
            
            # 提取日期信息
            date_elements = self._extract_dates(full_text)
            elements.extend(date_elements)
            
            return elements
            
        except Exception as e:
            logger.error(f"同步要素提取失败: {e}")
            return []
    
    def _extract_title(self, paragraphs) -> Optional[ExtractedElement]:
        """提取合同标题"""
        title_patterns = [
            r'(.{5,50}?)(合同|协议|契约|协定)',
            r'^(.{5,50})$'  # 简单的首行标题
        ]
        
        # 检查前5段
        for i, paragraph in enumerate(paragraphs[:5]):
            text = paragraph.text.strip()
            if not text or len(text) < 5:
                continue
                
            for pattern in title_patterns:
                match = re.search(pattern, text)
                if match:
                    return ExtractedElement(
                        element_name="合同标题",
                        element_category=ElementCategory.DOCUMENT_STRUCTURE,
                        value=text,
                        confidence=0.9 if '合同' in text or '协议' in text else 0.6,
                        source_position={"paragraph": i, "text": text},
                        pattern_type="title_pattern"
                    )
        
        return None
    
    def _extract_parties(self, text: str) -> List[ExtractedElement]:
        """提取当事方信息 - 带调试功能的版本"""
        elements = []

        logger.info("🔍 开始当事方信息提取调试")
        logger.info(f"📄 文本长度: {len(text)} 字符")

        # 改进的当事方提取模式 - 支持多种格式
        party_patterns = [
            # 括号格式：委托方（甲方）：公司名称
            (r'委托方\s*[（(]\s*甲\s*方\s*[）)]\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:住所|地址|联系|电话|传真|邮编|法定代表人|项目联系人|$))', '甲方'),
            (r'受托方\s*[（(]\s*乙\s*方\s*[）)]\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:住所|地址|联系|电话|传真|邮编|法定代表人|项目联系人|$))', '乙方'),

            # 反向括号格式：甲方（委托方）：公司名称
            (r'甲\s*方\s*[（(]\s*委托方\s*[）)]\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:住所|地址|联系|电话|传真|邮编|法定代表人|项目联系人|$))', '甲方'),
            (r'乙\s*方\s*[（(]\s*受托方\s*[）)]\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:住所|地址|联系|电话|传真|邮编|法定代表人|项目联系人|$))', '乙方'),

            # 标准格式：甲方：公司名称
            (r'甲\s*方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:乙方|住所|地址|联系|电话|传真|邮编|法定代表人|委托代理人|项目联系人|$))', '甲方'),
            (r'乙\s*方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:甲方|住所|地址|联系|电话|传真|邮编|法定代表人|委托代理人|项目联系人|$))', '乙方'),

            # 委托方格式
            (r'委托方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:受托方|住所|地址|联系|电话|传真|邮编|法定代表人|项目联系人|$))', '委托方'),
            (r'受托方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:委托方|住所|地址|联系|电话|传真|邮编|法定代表人|项目联系人|$))', '受托方'),

            # 出租方/承租方格式
            (r'出租方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:承租方|住所|地址|联系|$))', '出租方'),
            (r'承租方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:出租方|住所|地址|联系|$))', '承租方'),

            # 买方/卖方格式
            (r'买\s*方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:卖方|住所|地址|联系|$))', '买方'),
            (r'卖\s*方\s*[：:]\s*([^\n\r]{3,100}?)(?=\s*\n\s*(?:买方|住所|地址|联系|$))', '卖方'),

            # 带地址格式：甲方：公司名称 地址：xxx
            (r'甲\s*方\s*[：:]\s*([^\n\r]+?)\s*(?:住所|地址)', '甲方'),
            (r'乙\s*方\s*[：:]\s*([^\n\r]+?)\s*(?:住所|地址)', '乙方'),

            # 更宽松的模式作为最后后备
            (r'甲\s*方\s*[：:]\s*([^\n\r]{3,200}?)(?=\s*\n)', '甲方'),
            (r'乙\s*方\s*[：:]\s*([^\n\r]{3,200}?)(?=\s*\n)', '乙方'),
        ]

        for i, (pattern, party_type) in enumerate(party_patterns):
            logger.info(f"🔎 尝试模式 {i+1} ({party_type}): {pattern[:50]}...")

            matches = list(re.finditer(pattern, text, re.MULTILINE | re.IGNORECASE))
            logger.info(f"   找到 {len(matches)} 个匹配")

            for j, match in enumerate(matches):
                party_info = match.group(1).strip()
                logger.info(f"   匹配 {j+1}: '{party_info[:100]}{'...' if len(party_info) > 100 else ''}'")

                # 内容验证
                is_valid = self._is_valid_party_info(party_info)
                logger.info(f"   验证结果: {'✅ 有效' if is_valid else '❌ 无效'}")

                if is_valid:
                    # 清理和标准化
                    cleaned_info = self._clean_party_info(party_info)
                    logger.info(f"   清理后: '{cleaned_info}'")

                    # 计算置信度
                    confidence = self._calculate_party_confidence(cleaned_info)
                    logger.info(f"   置信度: {confidence:.2f}")

                    elements.append(ExtractedElement(
                        element_name=party_type,
                        element_category=ElementCategory.PARTY_INFO,
                        value=cleaned_info,
                        confidence=confidence,
                        source_position={"start": match.start(), "end": match.end()},
                        pattern_type="party_pattern_enhanced"
                    ))

                    logger.info(f"   ✅ 成功添加 {party_type}: {cleaned_info}")
                    # 每种类型只取第一个有效匹配
                    break
                else:
                    # 输出验证失败的详细原因
                    self._debug_validation_failure(party_info)

        logger.info(f"🎯 当事方提取完成: 共找到 {len(elements)} 个有效当事方")
        return elements

    def _debug_validation_failure(self, party_info: str):
        """调试验证失败的原因"""
        logger.info(f"   🔍 验证失败详情:")
        logger.info(f"     长度: {len(party_info)} 字符")

        # 检查无效关键词
        invalid_keywords = [
            '保密', '技术', '研究', '开发', '项目', '内容', '信息', '文件',
            '条款', '协议', '合同', '责任', '义务', '权利', '损失', '赔偿',
            '通知', '变更', '终止', '解除', '违约', '争议', '仲裁',
            '应当', '必须', '可以', '不得', '如果', '但是', '除非',
            '包括', '所有', '任何', '其他', '以上', '以下', '按照'
        ]

        found_invalid = [kw for kw in invalid_keywords if kw in party_info.lower()]
        if found_invalid:
            logger.info(f"     包含无效关键词: {found_invalid}")

        # 检查公司关键词
        company_keywords = [
            '公司', '企业', '集团', '有限', '股份', '责任', 'company', 'corp', 'ltd', 'inc',
            '中心', '院', '所', '部', '局', '委员会', '协会', '基金会'
        ]

        found_company = [kw for kw in company_keywords if kw in party_info.lower()]
        if found_company:
            logger.info(f"     包含公司关键词: {found_company}")
        else:
            logger.info(f"     未找到公司关键词")

        # 检查个人姓名
        has_person_name = bool(re.search(r'[\u4e00-\u9fa5]{2,4}(?=\s|$)', party_info))
        logger.info(f"     包含个人姓名: {'是' if has_person_name else '否'}")

    def _is_valid_party_info(self, party_info: str) -> bool:
        """验证当事方信息是否有效 - 放宽条件版本"""
        if not party_info or len(party_info.strip()) < 2:
            return False

        party_info_lower = party_info.lower().strip()

        # 明确的无效内容（必须完全排除）
        definite_invalid_patterns = [
            r'保密内容.*?技术.*?商务文件',  # 保密条款
            r'应当保证.*?研究开发成果',     # 义务条款
            r'双方经过.*?协商.*?基础上',    # 协商条款
            r'技术风险.*?研究开发失败',     # 风险条款
            r'逾期未通知.*?赔偿责任',      # 违约条款
        ]

        # 检查是否匹配明确无效的模式
        for pattern in definite_invalid_patterns:
            if re.search(pattern, party_info, re.IGNORECASE):
                return False

        # 如果内容过长且包含很多条款性词汇，可能是条款内容
        if len(party_info) > 100:
            clause_keywords = ['应当', '必须', '不得', '负责', '承担', '保证', '确保', '履行']
            clause_count = sum(1 for keyword in clause_keywords if keyword in party_info)
            if clause_count > 3:
                return False

        # 积极验证：包含这些特征的内容很可能是有效的当事方信息
        positive_indicators = [
            # 公司相关
            '公司', '企业', '集团', '有限', '股份', '责任', 'company', 'corp', 'ltd', 'inc',
            '中心', '院', '所', '部', '局', '委员会', '协会', '基金会',
            # 地名（常见的公司注册地）
            '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '西安', '重庆',
            '天津', '苏州', '无锡', '宁波', '青岛', '大连', '厦门', '长沙', '郑州', '济南',
            # 个人姓名特征
        ]

        has_positive_indicator = any(indicator in party_info_lower for indicator in positive_indicators)

        # 检查是否像个人姓名（2-4个中文字符，且不包含明显的非姓名词汇）
        person_name_pattern = r'^[\u4e00-\u9fa5]{2,4}$'
        is_person_name = bool(re.match(person_name_pattern, party_info.strip()))

        # 如果有积极指标或者是个人姓名，认为有效
        if has_positive_indicator or is_person_name:
            return True

        # 对于其他情况，如果长度合理且不包含明显的条款词汇，也认为可能有效
        if 3 <= len(party_info.strip()) <= 50:
            # 检查是否包含太多条款性词汇
            clause_indicators = ['条款', '协议', '合同', '责任', '义务', '权利', '应当', '必须', '不得']
            clause_count = sum(1 for indicator in clause_indicators if indicator in party_info_lower)

            if clause_count <= 1:  # 允许包含少量条款词汇
                return True

        return False

    def _clean_party_info(self, party_info: str) -> str:
        """清理和标准化当事方信息"""
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', party_info.strip())

        # 移除常见的无关后缀
        suffixes_to_remove = [
            r'\s*（以下简称.*?）',
            r'\s*\(以下简称.*?\)',
            r'\s*，以下简称.*?[，。]',
            r'\s*,以下简称.*?[,.]'
        ]

        for suffix_pattern in suffixes_to_remove:
            cleaned = re.sub(suffix_pattern, '', cleaned, flags=re.IGNORECASE)

        # 截取到第一个句号或分号
        if '。' in cleaned:
            cleaned = cleaned.split('。')[0]
        if '；' in cleaned:
            cleaned = cleaned.split('；')[0]
        if ';' in cleaned:
            cleaned = cleaned.split(';')[0]

        return cleaned.strip()

    def _calculate_party_confidence(self, party_info: str) -> float:
        """计算当事方信息的置信度"""
        base_confidence = 0.7

        # 包含公司关键词提高置信度
        company_keywords = ['有限公司', '股份公司', '集团', '企业', 'company', 'corp', 'ltd']
        if any(keyword in party_info.lower() for keyword in company_keywords):
            base_confidence += 0.15

        # 长度适中提高置信度
        if 5 <= len(party_info) <= 50:
            base_confidence += 0.1
        elif len(party_info) > 100:
            base_confidence -= 0.1

        # 包含地名提高置信度
        location_keywords = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '西安', '重庆']
        if any(location in party_info for location in location_keywords):
            base_confidence += 0.05

        return min(base_confidence, 1.0)
    
    def _extract_amounts(self, text: str) -> List[ExtractedElement]:
        """提取金额信息"""
        elements = []
        amount_patterns = [
            r'(金额|价格|费用|总价)[：:]?\s*([0-9,，]+\.?[0-9]*)\s*(元|万元|USD|美元)',
            r'([0-9,，]+\.?[0-9]*)\s*(元|万元|USD|美元)'
        ]
        
        for pattern in amount_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                amount_text = match.group()
                elements.append(ExtractedElement(
                    element_name="合同金额",
                    element_category=ElementCategory.FINANCIAL_TERMS,
                    value=amount_text,
                    confidence=0.8,
                    source_position={"start": match.start(), "end": match.end()},
                    pattern_type="amount_pattern"
                ))
        
        return elements
    
    def _extract_dates(self, text: str) -> List[ExtractedElement]:
        """提取日期信息 - 智能识别日期类型"""
        elements = []

        # 改进的日期提取模式 - 包含上下文
        date_patterns = [
            # 明确标识的日期
            (r'(签署日期|签订日期|合同签署日期)[：:]\s*(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', '合同签署日期'),
            (r'(生效日期|合同生效日期)[：:]\s*(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', '合同生效日期'),
            (r'(到期日期|合同到期日期|终止日期)[：:]\s*(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', '合同到期日期'),
            (r'(开始日期|项目开始日期|工作开始日期)[：:]\s*(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', '项目开始日期'),
            (r'(完成日期|项目完成日期|交付日期)[：:]\s*(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日', '项目完成日期'),

            # 带格式的日期
            (r'(签署日期|签订日期)[：:]\s*(\d{4})[/-](\d{1,2})[/-](\d{1,2})', '合同签署日期'),
            (r'(生效日期)[：:]\s*(\d{4})[/-](\d{1,2})[/-](\d{1,2})', '合同生效日期'),
            (r'(到期日期|终止日期)[：:]\s*(\d{4})[/-](\d{1,2})[/-](\d{1,2})', '合同到期日期'),

            # 上下文推断的日期
            (r'自\s*(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日\s*起', '合同生效日期'),
            (r'至\s*(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日\s*止', '合同到期日期'),
            (r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日\s*签署', '合同签署日期'),
            (r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日\s*生效', '合同生效日期'),
        ]

        found_dates = set()  # 避免重复提取相同日期

        for pattern, date_type in date_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                # 提取日期值
                if len(match.groups()) >= 4:  # 包含日期类型标识的格式
                    year = match.group(2)
                    month = match.group(3)
                    day = match.group(4)
                else:  # 简单格式
                    year = match.group(1)
                    month = match.group(2)
                    day = match.group(3)

                date_value = f"{year}年{month}月{day}日"
                date_key = f"{date_type}:{date_value}"

                if date_key not in found_dates:
                    found_dates.add(date_key)

                    # 计算置信度
                    confidence = self._calculate_date_confidence(match.group(), date_type)

                    elements.append(ExtractedElement(
                        element_name=date_type,
                        element_category=ElementCategory.TIME_TERMS,
                        value=date_value,
                        confidence=confidence,
                        source_position={"start": match.start(), "end": match.end()},
                        pattern_type="date_pattern_enhanced"
                    ))

        # 通用日期模式（作为后备，但尝试从上下文推断类型）
        generic_patterns = [
            r'(\d{4})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日',
            r'(\d{4})[/-](\d{1,2})[/-](\d{1,2})'
        ]

        for pattern in generic_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                year = match.group(1)
                month = match.group(2)
                day = match.group(3)
                date_value = f"{year}年{month}月{day}日"

                # 尝试从上下文推断日期类型
                context = text[max(0, match.start()-50):match.end()+50]
                inferred_type = self._infer_date_type_from_context(context, date_value)

                date_key = f"{inferred_type}:{date_value}"
                if date_key not in found_dates:
                    found_dates.add(date_key)

                    elements.append(ExtractedElement(
                        element_name=inferred_type,
                        element_category=ElementCategory.TIME_TERMS,
                        value=date_value,
                        confidence=0.6,  # 推断的置信度较低
                        source_position={"start": match.start(), "end": match.end()},
                        pattern_type="date_pattern_inferred"
                    ))

        return elements

    def _calculate_date_confidence(self, match_text: str, date_type: str) -> float:
        """计算日期提取的置信度"""
        base_confidence = 0.8

        # 明确标识的日期置信度更高
        if any(keyword in match_text for keyword in ['签署', '生效', '到期', '开始', '完成']):
            base_confidence = 0.9

        # 格式标准的日期置信度更高
        if '年' in match_text and '月' in match_text and '日' in match_text:
            base_confidence += 0.05

        return min(base_confidence, 1.0)

    def _infer_date_type_from_context(self, context: str, date_value: str) -> str:
        """从上下文推断日期类型"""
        context_lower = context.lower()

        # 签署相关关键词
        if any(keyword in context_lower for keyword in ['签署', '签订', '签字', '签名', '签约']):
            return '合同签署日期'

        # 生效相关关键词
        if any(keyword in context_lower for keyword in ['生效', '开始', '起', '自']):
            return '合同生效日期'

        # 到期相关关键词
        if any(keyword in context_lower for keyword in ['到期', '终止', '结束', '止', '至']):
            return '合同到期日期'

        # 项目相关关键词
        if any(keyword in context_lower for keyword in ['项目', '工作', '开发', '完成', '交付']):
            if any(keyword in context_lower for keyword in ['开始', '启动']):
                return '项目开始日期'
            elif any(keyword in context_lower for keyword in ['完成', '交付', '结束']):
                return '项目完成日期'

        # 默认为重要日期
        return '重要日期'
