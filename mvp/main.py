"""
AI合同审核系统 - MVP技术验证
基于FastAPI + python-docx + 千问3 API的智能合同分析系统
"""

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass  # python-dotenv未安装时忽略

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
import asyncio
import logging
from typing import Dict, List, Optional
from pathlib import Path
import tempfile
import os

# 导入核心模块
from document_processor import DocumentProcessor
from contract_analyzer import ContractAnalyzer
from models import ContractAnalysisResult, DocumentInfo

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="AI合同审核系统 MVP",
    description="技术验证原型 - 核心功能演示",
    version="0.1.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境需要限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件服务
app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化核心组件
document_processor = DocumentProcessor()
contract_analyzer = ContractAnalyzer()

@app.get("/")
async def root():
    """重定向到Web界面"""
    return RedirectResponse(url="/static/index.html")

@app.get("/api/v1/status")
async def api_status():
    """API状态检查接口"""
    return {
        "message": "AI合同审核系统 MVP",
        "status": "running",
        "version": "0.1.0"
    }

@app.post("/api/v1/analyze-contract", response_model=ContractAnalysisResult)
async def analyze_contract(file: UploadFile = File(...)):
    """
    合同分析核心接口
    
    技术验证重点：
    1. FastAPI异步文件上传处理
    2. python-docx文档解析能力
    3. 异步AI API调用模拟
    4. 错误处理和响应机制
    """
    
    # 验证文件格式
    if not file.filename.endswith(('.doc', '.docx')):
        raise HTTPException(
            status_code=400,
            detail="仅支持Word文档格式 (.doc/.docx)"
        )
    
    # 验证文件大小 (50MB限制)
    if file.size and file.size > 50 * 1024 * 1024:
        raise HTTPException(
            status_code=400,
            detail="文件大小不能超过50MB"
        )
    
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            # 异步读取上传文件内容
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        logger.info(f"开始处理文件: {file.filename}, 大小: {len(content)} bytes")
        
        # 步骤1: 文档解析和结构化处理
        document_info = await document_processor.process_document(temp_file_path)
        # 修正文件名为原始文件名
        document_info.filename = file.filename
        logger.info(f"文档解析完成: {document_info.total_paragraphs} 段落")
        
        # 步骤2: 合同类型识别和要素提取
        analysis_result = await contract_analyzer.analyze_contract(
            document_info=document_info,
            original_filename=file.filename,
            temp_file_path=temp_file_path
        )
        logger.info(f"合同分析完成: 类型={analysis_result.contract_type}")
        
        # 清理临时文件
        os.unlink(temp_file_path)
        
        return analysis_result
        
    except Exception as e:
        logger.error(f"合同分析失败: {str(e)}")
        # 确保清理临时文件
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        
        raise HTTPException(
            status_code=500,
            detail=f"合同分析失败: {str(e)}"
        )

@app.get("/api/v1/health")
async def health_check():
    """系统健康检查"""
    try:
        # 检查核心组件状态
        processor_status = await document_processor.health_check()
        analyzer_status = await contract_analyzer.health_check()
        
        return {
            "status": "healthy",
            "components": {
                "document_processor": processor_status,
                "contract_analyzer": analyzer_status
            },
            "timestamp": asyncio.get_event_loop().time()
        }
    except Exception as e:
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": asyncio.get_event_loop().time()
            }
        )

@app.get("/api/v1/supported-formats")
async def get_supported_formats():
    """获取支持的文件格式"""
    return {
        "supported_formats": [".doc", ".docx"],
        "max_file_size": "50MB",
        "description": "支持Microsoft Word文档格式"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
