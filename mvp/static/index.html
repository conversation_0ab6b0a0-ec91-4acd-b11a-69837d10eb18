<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI合同审核系统 MVP - 技术验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .upload-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #4facfe;
            background: #f0f8ff;
        }
        
        .upload-section.dragover {
            border-color: #4facfe;
            background: #e3f2fd;
            transform: scale(1.02);
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .file-info {
            margin-top: 20px;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 6px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .results-section {
            display: none;
            margin-top: 30px;
        }
        
        .result-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .result-card h3 {
            color: #495057;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .result-card h3::before {
            content: "✓";
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            display: none;
        }
        
        .tech-info {
            background: #f8f9fa;
            border-left: 4px solid #4facfe;
            padding: 20px;
            margin-top: 30px;
        }
        
        .tech-info h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .tech-list {
            list-style: none;
            padding: 0;
        }
        
        .tech-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .tech-list li:last-child {
            border-bottom: none;
        }
        
        .tech-list li::before {
            content: "⚡";
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI合同审核系统</h1>
            <p>MVP技术验证 - 核心功能演示</p>
        </div>
        
        <div class="content">
            <div class="upload-section" id="uploadSection">
                <h3>📄 上传合同文档</h3>
                <p>支持 .doc 和 .docx 格式，最大50MB</p>
                <input type="file" id="fileInput" class="file-input" accept=".doc,.docx">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
                <button class="upload-btn" id="analyzeBtn" onclick="analyzeContract()" style="display:none;">
                    开始分析
                </button>
                
                <div class="file-info" id="fileInfo">
                    <strong>已选择文件：</strong><span id="fileName"></span><br>
                    <strong>文件大小：</strong><span id="fileSize"></span>
                </div>
                
                <div class="progress-bar" id="progressBar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
            </div>
            
            <div class="error-message" id="errorMessage"></div>
            
            <div class="results-section" id="resultsSection">
                <h2>📊 分析结果</h2>
                
                <div class="result-card">
                    <h3>基础信息</h3>
                    <div id="basicInfo"></div>
                </div>
                
                <div class="result-card">
                    <h3>合同类型识别</h3>
                    <div id="contractType"></div>
                </div>
                
                <div class="result-card">
                    <h3>要素提取结果</h3>
                    <div id="extractedElements"></div>
                </div>
                
                <div class="result-card">
                    <h3>风险识别</h3>
                    <div id="riskPoints"></div>
                </div>
                
                <div class="result-card">
                    <h3>性能指标</h3>
                    <div id="performanceMetrics"></div>
                </div>
            </div>
            
            <div class="tech-info">
                <h3>🔧 技术验证重点</h3>
                <ul class="tech-list">
                    <li><strong>FastAPI异步处理</strong> - 验证高并发文件上传能力</li>
                    <li><strong>python-docx解析</strong> - 验证Word文档结构化提取</li>
                    <li><strong>AI API集成</strong> - 验证智能分析服务调用</li>
                    <li><strong>错误处理机制</strong> - 验证系统健壮性和容错能力</li>
                    <li><strong>性能监控</strong> - 验证处理速度和资源使用</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        
        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                selectedFile = file;
                showFileInfo(file);
            }
        });
        
        // 拖拽上传
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) {
                    selectedFile = file;
                    showFileInfo(file);
                } else {
                    showError('请选择 .doc 或 .docx 格式的文件');
                }
            }
        });
        
        function showFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileInfo').style.display = 'block';
            document.getElementById('analyzeBtn').style.display = 'inline-block';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        async function analyzeContract() {
            if (!selectedFile) {
                showError('请先选择文件');
                return;
            }
            
            // 显示进度条
            document.getElementById('progressBar').style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            
            try {
                // 模拟进度更新
                updateProgress(20);
                
                const response = await fetch('/api/v1/analyze-contract', {
                    method: 'POST',
                    body: formData
                });
                
                updateProgress(60);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '分析失败');
                }
                
                const result = await response.json();
                updateProgress(100);
                
                setTimeout(() => {
                    displayResults(result);
                    document.getElementById('progressBar').style.display = 'none';
                }, 500);
                
            } catch (error) {
                console.error('分析失败:', error);
                showError('分析失败: ' + error.message);
                document.getElementById('progressBar').style.display = 'none';
            }
        }
        
        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }
        
        function displayResults(result) {
            // 基础信息
            document.getElementById('basicInfo').innerHTML = `
                <p><strong>文件名：</strong>${result.document_info.filename}</p>
                <p><strong>文件大小：</strong>${formatFileSize(result.document_info.file_size)}</p>
                <p><strong>段落数：</strong>${result.document_info.total_paragraphs}</p>
                <p><strong>表格数：</strong>${result.document_info.total_tables}</p>
                <p><strong>总字数：</strong>${result.document_info.total_words}</p>
            `;
            
            // 合同类型
            document.getElementById('contractType').innerHTML = `
                <span class="status-indicator status-success">${result.contract_type}</span>
                <p style="margin-top: 10px;">置信度: ${(result.overall_confidence * 100).toFixed(1)}%</p>
            `;
            
            // 要素提取
            let elementsHtml = '';
            if (result.extracted_elements && result.extracted_elements.length > 0) {
                result.extracted_elements.forEach(element => {
                    elementsHtml += `
                        <div style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            <strong>${element.element_name}</strong>: ${element.value}
                            <br><small>置信度: ${(element.confidence * 100).toFixed(1)}%</small>
                        </div>
                    `;
                });
            } else {
                elementsHtml = '<p>未提取到要素信息</p>';
            }
            document.getElementById('extractedElements').innerHTML = elementsHtml;
            
            // 风险点
            let risksHtml = '';
            if (result.risk_points && result.risk_points.length > 0) {
                result.risk_points.forEach(risk => {
                    const statusClass = risk.risk_level === 'high' ? 'status-error' : 
                                       risk.risk_level === 'medium' ? 'status-warning' : 'status-success';
                    risksHtml += `
                        <div style="margin-bottom: 15px; padding: 15px; border-left: 4px solid #dc3545; background: #f8f9fa;">
                            <div><span class="status-indicator ${statusClass}">${risk.risk_level}</span></div>
                            <h4 style="margin: 10px 0 5px 0;">${risk.risk_type}</h4>
                            <p>${risk.description}</p>
                            <small><strong>建议：</strong>${risk.suggestion}</small>
                        </div>
                    `;
                });
            } else {
                risksHtml = '<p style="color: #28a745;">✓ 未发现明显风险点</p>';
            }
            document.getElementById('riskPoints').innerHTML = risksHtml;
            
            // 性能指标
            document.getElementById('performanceMetrics').innerHTML = `
                <p><strong>处理时间：</strong>${result.processing_duration.toFixed(2)} 秒</p>
                <p><strong>整体置信度：</strong>${(result.overall_confidence * 100).toFixed(1)}%</p>
                <p><strong>完整性评分：</strong>${(result.completeness_score * 100).toFixed(1)}%</p>
                <p><strong>风险评分：</strong>${(result.risk_score * 100).toFixed(1)}%</p>
            `;
            
            document.getElementById('resultsSection').style.display = 'block';
        }
        
        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorMessage').style.display = 'block';
        }
        
        // 页面加载时检查API状态
        window.addEventListener('load', async function() {
            try {
                const response = await fetch('/api/v1/health');
                const health = await response.json();
                console.log('API状态:', health);
            } catch (error) {
                console.warn('API连接检查失败:', error);
            }
        });
    </script>
</body>
</html>
