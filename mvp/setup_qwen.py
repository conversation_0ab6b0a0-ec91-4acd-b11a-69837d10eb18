#!/usr/bin/env python3
"""
千问3 API配置向导
帮助用户快速配置千问3 API密钥和相关设置
"""

import os
import sys
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🤖 AI合同审核系统 - 千问3配置向导")
    print("=" * 60)
    print()

def check_existing_config():
    """检查现有配置"""
    env_file = Path(".env")
    if env_file.exists():
        print("✓ 发现现有的.env配置文件")
        
        # 读取现有配置
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "QWEN_API_KEY=" in content and not "your_qwen_api_key_here" in content:
            print("✓ 千问3 API密钥已配置")
            return True
        else:
            print("⚠️  千问3 API密钥未配置或使用示例值")
            return False
    else:
        print("ℹ️  未找到.env配置文件")
        return False

def get_qwen_api_key():
    """获取千问3 API密钥"""
    print("\n📋 千问3 API密钥配置")
    print("-" * 30)
    print("请访问以下地址获取千问3 API密钥：")
    print("🔗 https://dashscope.console.aliyun.com/")
    print()
    print("详细步骤：")
    print("1. 登录阿里云账号")
    print("2. 开通DashScope服务（模型服务灵积）")
    print("3. 在控制台左侧菜单选择 'API-KEY管理'")
    print("4. 点击 '创建新的API-KEY'")
    print("5. 复制生成的API密钥（格式类似：sk-xxxxxxxxxxxxxx）")
    print()
    print("💡 提示：API密钥通常以 'sk-' 开头，长度约为40-50个字符")
    print()
    
    while True:
        api_key = input("请输入您的千问3 API密钥: ").strip()
        
        if not api_key:
            print("❌ API密钥不能为空，请重新输入")
            continue
            
        if len(api_key) < 20:
            print("❌ API密钥长度似乎不正确，请检查后重新输入")
            continue
            
        # 简单验证格式
        if api_key.startswith("sk-") or len(api_key) > 30:
            return api_key
        else:
            confirm = input("⚠️  API密钥格式可能不正确，是否继续使用？(y/n): ").lower()
            if confirm in ['y', 'yes']:
                return api_key

def create_env_file(api_key):
    """创建.env配置文件"""
    print("\n📝 创建配置文件...")
    
    # 读取示例文件
    example_file = Path(".env.example")
    if not example_file.exists():
        print("❌ 未找到.env.example文件")
        return False
    
    with open(example_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换API密钥
    content = content.replace("your_qwen_api_key_here", api_key)
    
    # 写入.env文件
    env_file = Path(".env")
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✓ .env配置文件创建成功")
    return True

def test_configuration():
    """测试配置"""
    print("\n🧪 测试配置...")
    
    try:
        # 设置环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        from config_manager import config_manager
        
        if config_manager.is_ai_enabled():
            print("✓ 千问3 API配置有效")
            print(f"✓ API密钥: {config_manager.get('qwen_api.api_key')[:10]}...")
            print(f"✓ 模型: {config_manager.get('qwen_api.model_name')}")
            return True
        else:
            print("❌ 千问3 API配置无效")
            return False
            
    except ImportError:
        print("⚠️  python-dotenv未安装，跳过配置测试")
        print("   可以运行: pip install python-dotenv")
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查现有配置
    if check_existing_config():
        choice = input("\n是否重新配置千问3 API？(y/n): ").lower()
        if choice not in ['y', 'yes']:
            print("配置向导已退出")
            return
    
    # 获取API密钥
    api_key = get_qwen_api_key()
    
    # 创建配置文件
    if create_env_file(api_key):
        print("✓ 配置文件创建成功")
    else:
        print("❌ 配置文件创建失败")
        return
    
    # 测试配置
    test_configuration()
    
    print("\n🎉 千问3配置完成！")
    print("\n下一步：")
    print("1. 运行: python start_mvp.py")
    print("2. 访问: http://localhost:8000")
    print("3. 上传合同文档测试AI分析功能")
    print("\n" + "=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n配置向导已取消")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 配置过程中出现错误: {e}")
        sys.exit(1)
