#!/usr/bin/env python3
"""
AI合同审核系统 MVP 启动脚本
技术验证环境的快速启动和配置
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        logger.error("需要Python 3.8或更高版本")
        sys.exit(1)
    logger.info(f"Python版本检查通过: {sys.version}")

def install_dependencies():
    """安装依赖包"""
    logger.info("开始安装依赖包...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        logger.info("依赖包安装完成")
    except subprocess.CalledProcessError as e:
        logger.error(f"依赖包安装失败: {e}")
        sys.exit(1)

def create_test_directories():
    """创建测试目录"""
    directories = [
        "temp",
        "logs",
        "test_files"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"创建目录: {directory}")

def create_sample_config():
    """创建示例配置文件"""
    config_content = """# AI合同审核系统 MVP 配置文件

# 服务器配置
HOST = "0.0.0.0"
PORT = 8000
DEBUG = True

# 文件处理配置
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
SUPPORTED_FORMATS = [".doc", ".docx"]
TEMP_DIR = "./temp"

# AI API配置 (模拟)
AI_API_BASE_URL = "https://api.example.com/v1"
AI_API_KEY = "mock_api_key"
AI_API_TIMEOUT = 30
AI_MAX_RETRIES = 3

# 日志配置
LOG_LEVEL = "INFO"
LOG_DIR = "./logs"

# 技术验证标志
ENABLE_AI_MOCK = True  # 启用AI API模拟
ENABLE_PERFORMANCE_LOGGING = True  # 启用性能日志
"""
    
    with open("config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    logger.info("创建配置文件: config.py")

def run_health_check():
    """运行健康检查"""
    logger.info("执行系统健康检查...")

    try:
        # 检查核心模块导入
        from document_processor import DocumentProcessor
        from contract_analyzer import ContractAnalyzer
        from models import ContractAnalysisResult
        from config_manager import config_manager
        from qwen_api_client import QwenAPIClient

        logger.info("✓ 核心模块导入成功")

        # 检查依赖包
        import fastapi
        import uvicorn
        import httpx
        import docx
        import jieba

        logger.info("✓ 依赖包检查通过")

        # 检查配置
        config_manager.print_config_status()

        return True

    except ImportError as e:
        logger.error(f"✗ 模块导入失败: {e}")
        return False

def start_server():
    """启动开发服务器"""
    logger.info("启动AI合同审核系统 MVP...")
    logger.info("=" * 60)
    logger.info("🚀 AI合同审核系统 - 千问3集成版")
    logger.info("📋 核心功能:")
    logger.info("   - FastAPI异步文件上传")
    logger.info("   - python-docx文档解析")
    logger.info("   - 千问3智能分析 (如已配置API密钥)")
    logger.info("   - 规则匹配降级机制")
    logger.info("   - 合同要素提取")
    logger.info("   - 风险识别和缺失条款检测")
    logger.info("=" * 60)
    logger.info("🌐 访问地址: http://localhost:8000")
    logger.info("📖 API文档: http://localhost:8000/docs")
    logger.info("🔍 健康检查: http://localhost:8000/api/v1/health")
    logger.info("⚙️  配置千问3: 复制.env.example为.env并填入API密钥")
    logger.info("=" * 60)
    
    try:
        import uvicorn
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器启动失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    logger.info("AI合同审核系统 MVP 初始化")
    
    # 检查Python版本
    check_python_version()
    
    # 安装依赖
    if "--install-deps" in sys.argv:
        install_dependencies()
    
    # 创建必要目录
    create_test_directories()
    
    # 创建配置文件
    if not os.path.exists("config.py"):
        create_sample_config()
    
    # 健康检查
    if not run_health_check():
        logger.error("健康检查失败，请检查依赖安装")
        sys.exit(1)
    
    # 启动服务器
    if "--no-server" not in sys.argv:
        start_server()
    else:
        logger.info("跳过服务器启动 (--no-server)")

if __name__ == "__main__":
    main()
